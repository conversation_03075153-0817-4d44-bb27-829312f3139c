# MastVilla - Luxury Villa Booking App

A beautiful Flutter application for booking luxury villas around the world.

## Features

- **Elegant Splash Screen**: Animated splash screen with smooth transitions
- **Modern UI Design**: Clean and intuitive user interface
- **Villa Search**: Search for villas by location, dates, and more
- **Popular Destinations**: Browse popular destinations with villa counts
- **Featured Villas**: Discover featured luxury villas with details and pricing
- **Booking System**: Easy booking process (to be implemented)

## Architecture

The project follows a clean architecture approach with the following structure:

```
lib/
├── core/
│   ├── constants/
│   ├── network/
│   └── utils/
├── data/
│   ├── datasources/
│   ├── models/
│   └── repositories/
├── presentation/
│   ├── providers/
│   ├── screens/
│   └── widgets/
└── main.dart
```

## Technologies Used

- **Provider**: For state management
- **Dio**: For network requests
- **Animations**: Custom animations for enhanced user experience
- **Material Design 3**: Latest Material Design components and theming

## Getting Started

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Run `flutter run` to start the application

## Screenshots

(Screenshots will be added as the app develops)

## Future Enhancements

- User authentication
- Payment integration
- Booking management
- Reviews and ratings
- Favorites and saved searches
- Push notifications
