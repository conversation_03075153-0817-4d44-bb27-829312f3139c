import 'package:flutter/material.dart';
import '../../data/models/villa_model.dart';
import '../../data/models/api_villa_model.dart';

class ModernVillaCard extends StatelessWidget {
  final Villa? villa;
  final ApiVilla? apiVilla;
  final VoidCallback? onTap;
  final bool showFullDetails;

  const ModernVillaCard({
    super.key,
    this.villa,
    this.apiVilla,
    this.onTap,
    this.showFullDetails = false,
  }) : assert(
         villa != null || apiVilla != null,
         'Either villa or apiVilla must be provided',
       );

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Use apiVilla data if available, otherwise fall back to villa
    final String name = apiVilla?.name ?? villa?.name ?? '';
    final String location =
        apiVilla?.address.isNotEmpty == true
            ? apiVilla!.address
            : apiVilla?.area ?? villa?.location ?? '';
    final String imageUrl = apiVilla?.primaryImage ?? villa?.imageUrl ?? '';
    final double rating = apiVilla?.rating ?? villa?.rating ?? 0.0;
    final double price = apiVilla?.effectiveWeekdayPrice ?? villa?.price ?? 0.0;
    final String description = apiVilla?.desc ?? villa?.description ?? '';
    final bool isFeatured = apiVilla?.isFeatured ?? villa?.isFeatured ?? false;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image section with overlay information
            _buildImageSection(context, imageUrl, price, rating, isFeatured),

            // Content section
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Villa name and location
                  _buildTitleSection(context, name, location),

                  if (showFullDetails && apiVilla != null) ...[
                    const SizedBox(height: 12),
                    _buildDetailedInfo(context, apiVilla!),
                  ],

                  if (description.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    _buildDescription(context, description),
                  ],

                  const SizedBox(height: 12),
                  _buildAmenitiesSection(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection(
    BuildContext context,
    String imageUrl,
    double price,
    double rating,
    bool isFeatured,
  ) {
    return Stack(
      children: [
        // Main image
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
          child: Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(color: Colors.grey[200]),
            child:
                imageUrl.isNotEmpty
                    ? Image.network(
                      imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder:
                          (context, error, stackTrace) => Container(
                            color: Colors.grey[300],
                            child: const Icon(
                              Icons.image_not_supported,
                              size: 50,
                              color: Colors.grey,
                            ),
                          ),
                    )
                    : Container(
                      color: Colors.grey[300],
                      child: const Icon(
                        Icons.villa,
                        size: 50,
                        color: Colors.grey,
                      ),
                    ),
          ),
        ),

        // Featured badge
        if (isFeatured)
          Positioned(
            top: 12,
            left: 12,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Text(
                'Featured',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

        // Price tag
        Positioned(
          top: 12,
          right: 12,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '₹${(price / 1000).toStringAsFixed(0)}K/night',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ),

        // Rating
        if (rating > 0)
          Positioned(
            bottom: 12,
            right: 12,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.star, color: Colors.amber, size: 14),
                  const SizedBox(width: 2),
                  Text(
                    rating.toStringAsFixed(1),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTitleSection(
    BuildContext context,
    String name,
    String location,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          name,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                location,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailedInfo(BuildContext context, ApiVilla apiVilla) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            children: [
              _buildInfoChip(
                Icons.people,
                '${apiVilla.noOfMemberFrom}-${apiVilla.noOfMemberTo} guests',
              ),
              const SizedBox(width: 8),
              _buildInfoChip(Icons.bed, '${apiVilla.noOfRoom} rooms'),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildInfoChip(Icons.bathtub, '${apiVilla.noOfwashroom} baths'),
              const SizedBox(width: 8),
              if (apiVilla.isSwimmingPool) _buildInfoChip(Icons.pool, 'Pool'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescription(BuildContext context, String description) {
    return Text(
      description,
      style: TextStyle(fontSize: 14, color: Colors.grey[600], height: 1.4),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildAmenitiesSection(BuildContext context) {
    final List<Map<String, dynamic>> amenities = [];

    if (apiVilla != null) {
      if (apiVilla!.wifi) amenities.add({'icon': Icons.wifi, 'label': 'WiFi'});
      if (apiVilla!.ac) amenities.add({'icon': Icons.ac_unit, 'label': 'AC'});
      if (apiVilla!.parking)
        amenities.add({'icon': Icons.local_parking, 'label': 'Parking'});
      if (apiVilla!.kitchen)
        amenities.add({'icon': Icons.kitchen, 'label': 'Kitchen'});
      if (apiVilla!.chef)
        amenities.add({'icon': Icons.restaurant, 'label': 'Chef'});
      if (apiVilla!.petFriendly)
        amenities.add({'icon': Icons.pets, 'label': 'Pet Friendly'});
    } else if (villa?.amenities != null) {
      for (String amenity in villa!.amenities!.take(6)) {
        amenities.add({'icon': Icons.check_circle, 'label': amenity});
      }
    }

    if (amenities.isEmpty) return const SizedBox.shrink();

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children:
          amenities.take(6).map((amenity) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    amenity['icon'],
                    size: 12,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    amenity['label'],
                    style: const TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }
}
