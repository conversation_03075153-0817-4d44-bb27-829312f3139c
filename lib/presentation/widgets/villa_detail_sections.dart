import 'package:flutter/material.dart';
import '../../data/models/api_villa_model.dart';

class VillaCapacitySection extends StatelessWidget {
  final ApiVilla apiVilla;

  const VillaCapacitySection({super.key, required this.apiVilla});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Capacity & Rooms',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildCapacityItem(
                  Icons.people,
                  'Guests',
                  '${apiVilla.noOfMemberFrom}-${apiVilla.noOfMemberTo}',
                  theme,
                ),
              ),
              Expanded(
                child: _buildCapacityItem(
                  Icons.bed,
                  'Bedrooms',
                  '${apiVilla.noOfRoom}',
                  theme,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildCapacityItem(
                  Icons.bathtub,
                  'Bathrooms',
                  '${apiVilla.noOfwashroom}',
                  theme,
                ),
              ),
              Expanded(
                child: _buildCapacityItem(
                  Icons.living,
                  'Living Rooms',
                  '${apiVilla.noOfLivingRoom}',
                  theme,
                ),
              ),
            ],
          ),
          if (apiVilla.noOfBed > 0 || apiVilla.noOfMattress > 0) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                if (apiVilla.noOfBed > 0)
                  Expanded(
                    child: _buildCapacityItem(
                      Icons.single_bed,
                      'Beds',
                      '${apiVilla.noOfBed}',
                      theme,
                    ),
                  ),
                if (apiVilla.noOfMattress > 0)
                  Expanded(
                    child: _buildCapacityItem(
                      Icons.bed_outlined,
                      'Mattresses',
                      '${apiVilla.noOfMattress}',
                      theme,
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCapacityItem(
    IconData icon,
    String label,
    String value,
    ThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(icon, color: theme.colorScheme.primary, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
        ],
      ),
    );
  }
}

class VillaAmenitiesSection extends StatelessWidget {
  final ApiVilla apiVilla;

  const VillaAmenitiesSection({super.key, required this.apiVilla});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final amenities = _getAmenities();

    if (amenities.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Amenities',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children:
                amenities.map((amenity) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          amenity['icon'],
                          size: 16,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          amenity['label'],
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w500,
                            fontSize: 13,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getAmenities() {
    final List<Map<String, dynamic>> amenities = [];

    if (apiVilla.wifi) amenities.add({'icon': Icons.wifi, 'label': 'WiFi'});
    if (apiVilla.ac) {
      amenities.add({
        'icon': Icons.ac_unit,
        'label': apiVilla.noOfAc > 0 ? 'AC (${apiVilla.noOfAc})' : 'AC',
      });
    }
    if (apiVilla.parking)
      amenities.add({'icon': Icons.local_parking, 'label': 'Parking'});
    if (apiVilla.kitchen)
      amenities.add({'icon': Icons.kitchen, 'label': 'Kitchen'});
    if (apiVilla.chef)
      amenities.add({'icon': Icons.restaurant, 'label': 'Chef Available'});
    if (apiVilla.meals)
      amenities.add({'icon': Icons.restaurant_menu, 'label': 'Meals Included'});
    if (apiVilla.complimentary)
      amenities.add({
        'icon': Icons.card_giftcard,
        'label': 'Complimentary Services',
      });
    if (apiVilla.isSwimmingPool) {
      amenities.add({
        'icon': Icons.pool,
        'label':
            apiVilla.swimmingPoolMeasument.isNotEmpty
                ? 'Pool (${apiVilla.swimmingPoolMeasument})'
                : 'Swimming Pool',
      });
    }
    if (apiVilla.isKidSwimmingPool)
      amenities.add({'icon': Icons.child_friendly, 'label': 'Kids Pool'});
    if (apiVilla.barbeque)
      amenities.add({'icon': Icons.outdoor_grill, 'label': 'BBQ'});
    if (apiVilla.petFriendly)
      amenities.add({'icon': Icons.pets, 'label': 'Pet Friendly'});
    if (apiVilla.powerBackup)
      amenities.add({'icon': Icons.power, 'label': 'Power Backup'});
    if (apiVilla.fireExtinguisher) {
      amenities.add({
        'icon': Icons.fire_extinguisher,
        'label':
            apiVilla.noOfFireExtinguisher > 0
                ? 'Fire Safety (${apiVilla.noOfFireExtinguisher})'
                : 'Fire Safety',
      });
    }
    if (apiVilla.equipped)
      amenities.add({'icon': Icons.check_circle, 'label': 'Fully Equipped'});

    return amenities;
  }
}

class VillaPricingSection extends StatelessWidget {
  final ApiVilla apiVilla;

  const VillaPricingSection({super.key, required this.apiVilla});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pricing',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildPriceCard(
                  'Weekday',
                  apiVilla.weekDayPrice,
                  apiVilla.weekDayDiscountPrice,
                  theme,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildPriceCard(
                  'Weekend',
                  apiVilla.weekendPrice,
                  apiVilla.weekendDiscountPrice,
                  theme,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriceCard(
    String label,
    double originalPrice,
    double discountPrice,
    ThemeData theme,
  ) {
    final bool hasDiscount = discountPrice > 0 && discountPrice < originalPrice;
    final double displayPrice = hasDiscount ? discountPrice : originalPrice;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          if (hasDiscount) ...[
            Text(
              '₹${originalPrice.toStringAsFixed(0)}',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
                decoration: TextDecoration.lineThrough,
              ),
            ),
            Text(
              '₹${displayPrice.toStringAsFixed(0)}',
              style: TextStyle(
                color: theme.colorScheme.primary,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ] else ...[
            Text(
              '₹${displayPrice.toStringAsFixed(0)}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ],
          const Text(
            'per night',
            style: TextStyle(fontSize: 10, color: Colors.grey),
          ),
        ],
      ),
    );
  }
}

class VillaStaffSection extends StatelessWidget {
  final ApiVilla apiVilla;

  const VillaStaffSection({super.key, required this.apiVilla});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (apiVilla.staff == null && apiVilla.noOFcaretaker == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Staff & Services',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          if (apiVilla.staff != null) ...[
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage:
                      apiVilla.staff!.profilePicture.isNotEmpty
                          ? NetworkImage(apiVilla.staff!.profilePicture)
                          : null,
                  child:
                      apiVilla.staff!.profilePicture.isEmpty
                          ? const Icon(Icons.person)
                          : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${apiVilla.staff!.firstName} ${apiVilla.staff!.lastName}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      if (apiVilla.staff!.contactNumber.isNotEmpty)
                        Text(
                          apiVilla.staff!.contactNumber,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
          ],
          if (apiVilla.noOFcaretaker > 0)
            Row(
              children: [
                Icon(Icons.support_agent, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  '${apiVilla.noOFcaretaker} Caretaker${apiVilla.noOFcaretaker > 1 ? 's' : ''} Available',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
