import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:geolocator/geolocator.dart';

import '../../data/models/category_model.dart';
import '../../data/providers/villa_provider.dart';
import '../../data/providers/user_provider.dart';
import '../../core/utils/location_service.dart';
import '../../core/utils/service_locator.dart';

class FilterBottomSheet extends StatefulWidget {
  final VoidCallback? onFiltersApplied;

  const FilterBottomSheet({
    super.key,
    this.onFiltersApplied,
  });

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  // Filter state variables
  int? _selectedLocationId;
  int? _selectedCategoryId;
  int _memberFrom = 1;
  int _memberTo = 10;
  List<int> _selectedAmenities = [];
  double _priceFrom = 1000.0;
  double _priceTo = 10000.0;
  DateTime? _fromDate;
  DateTime? _toDate;
  double _radius = 1500.0; // in meters

  @override
  void initState() {
    super.initState();
    _initializeDefaults();
  }

  void _initializeDefaults() {
    // Set default dates (today to 3 days later)
    _fromDate = DateTime.now();
    _toDate = DateTime.now().add(const Duration(days: 3));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final villaProvider = Provider.of<VillaProvider>(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.symmetric(vertical: 12.0),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Villas',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.close,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),

          // Filter content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category Section
                  if (villaProvider.categories.isNotEmpty) ...[
                    _buildSectionTitle('Category'),
                    const SizedBox(height: 12),
                    _buildCategorySelector(villaProvider.categories),
                    const SizedBox(height: 24),
                  ],

                  // Date Range Section
                  _buildSectionTitle('Travel Dates'),
                  const SizedBox(height: 12),
                  _buildDateRangeSelector(),
                  const SizedBox(height: 24),

                  // Guest Count Section
                  _buildSectionTitle('Number of Guests'),
                  const SizedBox(height: 12),
                  _buildGuestSelector(),
                  const SizedBox(height: 24),

                  // Price Range Section
                  _buildSectionTitle('Price Range (per night)'),
                  const SizedBox(height: 12),
                  _buildPriceRangeSelector(),
                  const SizedBox(height: 24),

                  // Amenities Section
                  _buildSectionTitle('Amenities'),
                  const SizedBox(height: 12),
                  _buildAmenitiesSelector(),
                  const SizedBox(height: 24),

                  // Radius Section
                  _buildSectionTitle('Search Radius'),
                  const SizedBox(height: 12),
                  _buildRadiusSelector(),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),

          // Action buttons
          Container(
            padding: const EdgeInsets.fromLTRB(24, 16, 24, 32),
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              border: Border(
                top: BorderSide(
                  color: theme.dividerColor.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _clearFilters,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: theme.colorScheme.primary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      side: BorderSide(
                        color: theme.colorScheme.primary,
                        width: 1.5,
                      ),
                    ),
                    child: const Text(
                      'Clear All',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: const Text(
                      'Apply Filters',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    final theme = Theme.of(context);
    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: theme.colorScheme.onSurface,
      ),
    );
  }

  Widget _buildCategorySelector(List<Category> categories) {
    final theme = Theme.of(context);
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: categories.where((cat) => cat.id != 0).map((category) {
        final isSelected = _selectedCategoryId == category.id;
        return InkWell(
          onTap: () {
            setState(() {
              _selectedCategoryId = isSelected ? null : category.id;
            });
          },
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withOpacity(0.3),
                width: 1.5,
              ),
            ),
            child: Text(
              category.title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected
                    ? Colors.white
                    : theme.colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDateRangeSelector() {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM d, yyyy');

    return Row(
      children: [
        Expanded(
          child: _buildDateBox(
            context: context,
            label: 'Check-in',
            value: _fromDate != null ? dateFormat.format(_fromDate!) : 'Select',
            onTap: () => _selectDate(true),
            theme: theme,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildDateBox(
            context: context,
            label: 'Check-out',
            value: _toDate != null ? dateFormat.format(_toDate!) : 'Select',
            onTap: () => _selectDate(false),
            theme: theme,
          ),
        ),
      ],
    );
  }

  Widget _buildDateBox({
    required BuildContext context,
    required String label,
    required String value,
    required VoidCallback onTap,
    required ThemeData theme,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.3),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.calendar_today_outlined,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuestSelector() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.3),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'From',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () {
                      if (_memberFrom > 1) {
                        setState(() => _memberFrom--);
                      }
                    },
                    icon: const Icon(Icons.remove_circle_outline),
                    color: theme.colorScheme.primary,
                  ),
                  SizedBox(
                    width: 40,
                    child: Text(
                      _memberFrom.toString(),
                      textAlign: TextAlign.center,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() => _memberFrom++);
                    },
                    icon: const Icon(Icons.add_circle_outline),
                    color: theme.colorScheme.primary,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'To',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () {
                      if (_memberTo > _memberFrom) {
                        setState(() => _memberTo--);
                      }
                    },
                    icon: const Icon(Icons.remove_circle_outline),
                    color: theme.colorScheme.primary,
                  ),
                  SizedBox(
                    width: 40,
                    child: Text(
                      _memberTo.toString(),
                      textAlign: TextAlign.center,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() => _memberTo++);
                    },
                    icon: const Icon(Icons.add_circle_outline),
                    color: theme.colorScheme.primary,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRangeSelector() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.3),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '₹${_priceFrom.toInt()}',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
              Text(
                '₹${_priceTo.toInt()}',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          RangeSlider(
            values: RangeValues(_priceFrom, _priceTo),
            min: 500.0,
            max: 20000.0,
            divisions: 100,
            activeColor: theme.colorScheme.primary,
            inactiveColor: theme.colorScheme.primary.withOpacity(0.3),
            onChanged: (RangeValues values) {
              setState(() {
                _priceFrom = values.start;
                _priceTo = values.end;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAmenitiesSelector() {
    final theme = Theme.of(context);
    final villaProvider = Provider.of<VillaProvider>(context);
    
    // Show loading state if amenities are being fetched
    if (villaProvider.isAmenitiesLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show error state if amenities failed to load
    if (villaProvider.amenitiesError != null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Text(
          'Failed to load amenities',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.error,
          ),
        ),
      );
    }

    // Use amenities from API, or fallback to empty if none available
    final amenities = villaProvider.amenities;
    
    if (amenities.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Text(
          'No amenities available',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      );
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: amenities.map((amenity) {
        final isSelected = _selectedAmenities.contains(amenity.id);
        return InkWell(
          onTap: () {
            setState(() {
              if (isSelected) {
                _selectedAmenities.remove(amenity.id);
              } else {
                _selectedAmenities.add(amenity.id);
              }
            });
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? theme.colorScheme.primary.withOpacity(0.1)
                  : theme.colorScheme.surface,
              border: Border.all(
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withOpacity(0.3),
                width: 1.5,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getAmenityIcon(amenity.title),
                  size: 16,
                  color: isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 6),
                Text(
                  amenity.title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  // Helper method to get appropriate icon for amenity name
  IconData _getAmenityIcon(String amenityName) {
    final name = amenityName.toLowerCase();
    
    if (name.contains('wifi') || name.contains('wi-fi') || name.contains('internet')) {
      return Icons.wifi;
    } else if (name.contains('pool') || name.contains('swimming')) {
      return Icons.pool;
    } else if (name.contains('ac') || name.contains('air') || name.contains('conditioning')) {
      return Icons.ac_unit;
    } else if (name.contains('parking') || name.contains('car')) {
      return Icons.local_parking;
    } else if (name.contains('kitchen') || name.contains('cook')) {
      return Icons.kitchen;
    } else if (name.contains('bbq') || name.contains('grill') || name.contains('barbecue')) {
      return Icons.outdoor_grill;
    } else if (name.contains('pet') || name.contains('dog') || name.contains('cat')) {
      return Icons.pets;
    } else if (name.contains('gym') || name.contains('fitness') || name.contains('exercise')) {
      return Icons.fitness_center;
    } else if (name.contains('tv') || name.contains('television')) {
      return Icons.tv;
    } else if (name.contains('balcony') || name.contains('terrace')) {
      return Icons.balcony;
    } else if (name.contains('garden') || name.contains('outdoor')) {
      return Icons.grass;
    } else if (name.contains('spa') || name.contains('massage')) {
      return Icons.spa;
    } else if (name.contains('laundry') || name.contains('wash')) {
      return Icons.local_laundry_service;
    } else if (name.contains('safe') || name.contains('security')) {
      return Icons.security;
    } else {
      return Icons.check_circle_outline; // Default icon
    }
  }

  Widget _buildRadiusSelector() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.3),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Within ${(_radius / 1000).toStringAsFixed(1)} km',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Slider(
            value: _radius,
            min: 500.0,
            max: 5000.0,
            divisions: 18,
            activeColor: theme.colorScheme.primary,
            inactiveColor: theme.colorScheme.primary.withOpacity(0.3),
            onChanged: (double value) {
              setState(() {
                _radius = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(bool isFromDate) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: isFromDate 
          ? (_fromDate ?? DateTime.now()) 
          : (_toDate ?? DateTime.now().add(const Duration(days: 1))),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        final theme = Theme.of(context);
        return Theme(
          data: theme.copyWith(
            colorScheme: theme.colorScheme.copyWith(
              primary: theme.colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = pickedDate;
          // Ensure toDate is after fromDate
          if (_toDate != null && _toDate!.isBefore(_fromDate!)) {
            _toDate = _fromDate!.add(const Duration(days: 1));
          }
        } else {
          _toDate = pickedDate;
          // Ensure fromDate is before toDate
          if (_fromDate != null && _fromDate!.isAfter(_toDate!)) {
            _fromDate = _toDate!.subtract(const Duration(days: 1));
          }
        }
      });
    }
  }

  void _clearFilters() {
    setState(() {
      _selectedLocationId = null;
      _selectedCategoryId = null;
      _memberFrom = 1;
      _memberTo = 10;
      _selectedAmenities.clear();
      _priceFrom = 1000.0;
      _priceTo = 10000.0;
      _fromDate = DateTime.now();
      _toDate = DateTime.now().add(const Duration(days: 3));
      _radius = 1500.0;
    });
  }

  Future<void> _applyFilters() async {
    try {
      // Get current location
      Position position = await LocationService.getCachedOrCurrentLocation();
      
      // Get user provider
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final villaProvider = Provider.of<VillaProvider>(context, listen: false);

      // Create search data according to the API schema
      final Map<String, dynamic> searchData = {
        'locationId': _selectedLocationId ?? 1, // Default locationId if not selected
        'categoryId': _selectedCategoryId ?? 1, // Default categoryId if not selected
        'memberFrom': _memberFrom,
        'memberTo': _memberTo,
        'amenitiesMasterIds': _selectedAmenities,
        'priceFrom': _priceFrom.toInt(),
        'priceTo': _priceTo.toInt(),
        'fromDate': _fromDate?.toIso8601String().split('T')[0] ?? DateTime.now().toIso8601String().split('T')[0],
        'toDate': _toDate?.toIso8601String().split('T')[0] ?? DateTime.now().add(const Duration(days: 3)).toIso8601String().split('T')[0],
        'radius': _radius.toInt(),
      };

      sl.logger.d('Applying filters with data: $searchData');

      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Perform map search (stores results separately from home screen data)
      await villaProvider.performMapSearch(searchData);

      // Close loading dialog
      if (mounted) {
        Navigator.pop(context);
      }

      // Close filter bottom sheet
      if (mounted) {
        Navigator.pop(context);
      }

      // Call callback if provided
      if (widget.onFiltersApplied != null) {
        widget.onFiltersApplied!();
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Found ${villaProvider.mapSearchResults.length} villa(s) matching your filters',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      sl.logger.e('Error applying filters: $e');
      
      // Close loading dialog if it's open
      if (mounted) {
        Navigator.pop(context);
      }

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to apply filters. Please try again.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
