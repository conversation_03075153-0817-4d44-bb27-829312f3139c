import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../data/models/villa_model.dart';
import '../../data/providers/villa_provider.dart';
import '../../routes.dart';
import '../widgets/modern_villa_card.dart';

class SimpleMapScreen extends StatefulWidget {
  const SimpleMapScreen({super.key});

  @override
  State<SimpleMapScreen> createState() => _SimpleMapScreenState();
}

class _SimpleMapScreenState extends State<SimpleMapScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Search bar
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Text<PERSON>ield(
                        controller: _searchController,
                        decoration: const InputDecoration(
                          hintText: 'Search location or villa name',
                          border: InputBorder.none,
                          icon: Icon(Icons.search, color: Colors.grey),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.filter_list),
                      onPressed: () {
                        // Filter functionality
                      },
                    ),
                  ),
                ],
              ),
            ),

            // Map placeholder
            Expanded(
              child: Container(
                color: Colors.grey[200],
                child: Stack(
                  children: [
                    // Map background
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.map_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Map View',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Location markers
                    Positioned(
                      left: MediaQuery.of(context).size.width * 0.5,
                      top: MediaQuery.of(context).size.height * 0.3,
                      child: _buildMarker('1'),
                    ),
                    Positioned(
                      left: MediaQuery.of(context).size.width * 0.3,
                      top: MediaQuery.of(context).size.height * 0.4,
                      child: _buildMarker('2'),
                    ),
                    Positioned(
                      left: MediaQuery.of(context).size.width * 0.7,
                      top: MediaQuery.of(context).size.height * 0.2,
                      child: _buildMarker('3'),
                    ),
                  ],
                ),
              ),
            ),

            // Villa cards at the bottom
            SizedBox(height: 200, child: _buildVillaCards()),
          ],
        ),
      ),
    );
  }

  Widget _buildMarker(String villaId) {
    return GestureDetector(
      onTap: () {
        Navigator.of(
          context,
        ).push(AppRoutes.generateVillaDetailRoute(context, villaId));
      },
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.red,
          shape: BoxShape.circle,
        ),
        padding: const EdgeInsets.all(8),
        child: const Icon(Icons.location_on, color: Colors.white, size: 24),
      ),
    );
  }

  Widget _buildVillaCards() {
    final villaProvider = Provider.of<VillaProvider>(context);
    final villas = villaProvider.featuredVillas.take(3).toList();

    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: villas.length,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemBuilder: (context, index) {
        final villa = villas[index];
        return Container(
          width: 280,
          margin: const EdgeInsets.only(right: 16),
          child: ModernVillaCard(
            villa: villa,
            onTap: () {
              Navigator.of(
                context,
              ).push(AppRoutes.generateVillaDetailRoute(context, villa.id));
            },
          ),
        );
      },
    );
  }
}
