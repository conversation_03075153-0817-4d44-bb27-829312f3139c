import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../data/models/chat_message_model.dart';

class ChatDetailScreen extends StatefulWidget {
  final String conversationId;
  final String hostName;
  final String hostAvatar;
  final String villaName;
  
  const ChatDetailScreen({
    super.key,
    required this.conversationId,
    required this.hostName,
    required this.hostAvatar,
    required this.villaName,
  });

  @override
  State<ChatDetailScreen> createState() => _ChatDetailScreenState();
}

class _ChatDetailScreenState extends State<ChatDetailScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final String _currentUserId = 'user123'; // Mock current user ID
  
  // Mock messages for the conversation
  late List<ChatMessage> _messages;
  
  @override
  void initState() {
    super.initState();
    _initializeMessages();
  }
  
  void _initializeMessages() {
    // Create mock booking info
    final bookingInfo = BookingInfo(
      villaId: 'villa123',
      villaName: 'Villa Serenity',
      villaImage: 'https://images.unsplash.com/photo-1580587771525-78b9dba3b914',
      checkInDate: DateTime(2024, 12, 15),
      checkOutDate: DateTime(2024, 12, 20),
    );
    
    // Create mock messages
    _messages = [
      ChatMessage(
        id: '1',
        senderId: 'host123',
        senderName: widget.hostName,
        senderAvatar: widget.hostAvatar,
        conversationId: widget.conversationId,
        content: 'Hi! Welcome to Villa Serenity. I\'m Sarah, your host.',
        timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        isRead: true,
        type: MessageType.text,
      ),
      ChatMessage(
        id: '2',
        senderId: 'host123',
        senderName: widget.hostName,
        senderAvatar: widget.hostAvatar,
        conversationId: widget.conversationId,
        content: 'Your check-in is scheduled for Dec 15. Would you like directions to the villa?',
        timestamp: DateTime.now().subtract(const Duration(minutes: 25)),
        isRead: true,
        type: MessageType.text,
      ),
      ChatMessage(
        id: '3',
        senderId: _currentUserId,
        conversationId: widget.conversationId,
        content: 'Hello Sarah! Yes, that would be very helpful.',
        timestamp: DateTime.now().subtract(const Duration(minutes: 20)),
        isRead: true,
        type: MessageType.text,
      ),
      ChatMessage(
        id: '4',
        senderId: 'host123',
        senderName: widget.hostName,
        senderAvatar: widget.hostAvatar,
        conversationId: widget.conversationId,
        content: 'Perfect! I\'ll send you the exact location and access instructions.',
        timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
        isRead: true,
        type: MessageType.text,
      ),
    ];
    
    // Add booking info message at the beginning
    _messages.insert(
      0,
      ChatMessage(
        id: '0',
        senderId: 'system',
        conversationId: widget.conversationId,
        content: 'Booking information',
        timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        type: MessageType.booking,
        metadata: bookingInfo.toJson(),
      ),
    );
  }
  
  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
  
  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;
    
    setState(() {
      _messages.add(
        ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          senderId: _currentUserId,
          conversationId: widget.conversationId,
          content: _messageController.text.trim(),
          timestamp: DateTime.now(),
          type: MessageType.text,
        ),
      );
      _messageController.clear();
    });
    
    // Scroll to bottom after sending message
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundImage: NetworkImage(widget.hostAvatar),
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.villaName,
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${widget.hostName} • Host',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.black),
            onPressed: () {
              // TODO: Show options menu
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Messages list
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                
                // Show date separator if needed
                Widget? dateSeparator;
                if (index == 0 || !_isSameDay(_messages[index - 1].timestamp, message.timestamp)) {
                  dateSeparator = _buildDateSeparator(message.timestamp);
                }
                
                return Column(
                  children: [
                    if (dateSeparator != null) dateSeparator,
                    _buildMessageItem(message),
                  ],
                );
              },
            ),
          ),
          
          // Message input
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: SafeArea(
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.attach_file),
                    onPressed: () {
                      // TODO: Implement file attachment
                    },
                  ),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: TextField(
                        controller: _messageController,
                        decoration: const InputDecoration(
                          hintText: 'Type a message...',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(vertical: 10),
                        ),
                        textCapitalization: TextCapitalization.sentences,
                        onSubmitted: (_) => _sendMessage(),
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.send, color: Colors.blue),
                    onPressed: _sendMessage,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildDateSeparator(DateTime date) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _formatDate(date),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildMessageItem(ChatMessage message) {
    final isCurrentUser = message.senderId == _currentUserId;
    
    // Handle booking info message type
    if (message.type == MessageType.booking) {
      return _buildBookingInfoMessage(message);
    }
    
    return Align(
      alignment: isCurrentUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        decoration: BoxDecoration(
          color: isCurrentUser ? Colors.blue : Colors.grey[200],
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: Text(
          message.content,
          style: TextStyle(
            color: isCurrentUser ? Colors.white : Colors.black,
          ),
        ),
      ),
    );
  }
  
  Widget _buildBookingInfoMessage(ChatMessage message) {
    final bookingData = BookingInfo.fromJson(message.metadata!);
    final dateFormat = DateFormat('MMM d, yyyy');
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Villa image and name
          Row(
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
                child: Image.network(
                  bookingData.villaImage,
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Your stay at ${bookingData.villaName}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${dateFormat.format(bookingData.checkInDate)} - ${dateFormat.format(bookingData.checkOutDate)}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () {
                        // TODO: Navigate to booking details
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'View Details',
                            style: TextStyle(
                              color: Colors.blue[700],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Icon(
                            Icons.chevron_right,
                            size: 16,
                            color: Colors.blue[700],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    if (date.year == now.year && date.month == now.month && date.day == now.day) {
      return 'Today';
    } else if (date.year == now.year && date.month == now.month && date.day == now.day - 1) {
      return 'Yesterday';
    } else {
      return DateFormat('MMM d, yyyy').format(date);
    }
  }
  
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month && date1.day == date2.day;
  }
}
