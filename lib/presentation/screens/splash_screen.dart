import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../core/utils/shared_preferences_helper.dart';
import '../../data/providers/user_provider.dart';
import '../../routes.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _loadingAnimation;

  @override
  void initState() {
    super.initState();

    // Set status bar to transparent
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    // Create fade animation
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
      ),
    );

    // Create loading animation
    _loadingAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.6, 1.0, curve: Curves.easeInOut),
      ),
    );

    // Start animation
    _animationController.forward();

    // Check authentication status after animation
    Timer(const Duration(seconds: 3), () {
      _checkAuthenticationStatus();
    });
  }

  Future<void> _checkAuthenticationStatus() async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final prefsHelper = SharedPreferencesHelper();
      
      // Check if user is already logged in
      final isLoggedIn = await prefsHelper.isLoggedIn();
      
      if (isLoggedIn) {
        // User is logged in, load user data and navigate to main screen
        await userProvider.loadUserFromPrefs();
        Navigator.of(context).pushReplacementNamed(AppRoutes.main);
      } else {
        // User is not logged in, check if first time
        final isFirstTime = await prefsHelper.isFirstTime();
        
        if (isFirstTime) {
          // First time user, go to onboarding
          await prefsHelper.setNotFirstTime();
          Navigator.of(context).pushReplacementNamed(AppRoutes.onboarding);
        } else {
          // Returning user but not logged in, go to login
          Navigator.of(context).pushReplacementNamed(AppRoutes.login);
        }
      }
    } catch (error) {
      // If there's any error, go to onboarding/login screen
      Navigator.of(context).pushReplacementNamed(AppRoutes.onboarding);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          // Background Image
          Image.network(
            'https://images.unsplash.com/photo-1582610116397-edb318620f90?q=80&w=1000',
            fit: BoxFit.cover,
          ),

          // Overlay
          Container(
            decoration: BoxDecoration(color: Colors.black.withAlpha(51)),
          ),

          // Content
          SafeArea(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Column(
                  children: [
                    // Status Bar Area (9:41, signal icons)
                    const SizedBox(height: 20),

                    // App Icon
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.grey.withAlpha(179),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.home,
                            size: 40,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    const Spacer(),

                    // App Name and Tagline
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: Column(
                        children: [
                          const Text(
                            'VillaVue',
                            style: TextStyle(
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Luxury Stays, Unforgettable Moments',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white,
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(height: 40),

                          // Loading Indicator
                          FadeTransition(
                            opacity: _loadingAnimation,
                            child: Column(
                              children: [
                                SizedBox(
                                  width: 40,
                                  height: 40,
                                  child: CircularProgressIndicator(
                                    valueColor:
                                        const AlwaysStoppedAnimation<Color>(
                                          Colors.white,
                                        ),
                                    strokeWidth: 3,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                const Text(
                                  'Loading your experience...',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Spacer(),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
