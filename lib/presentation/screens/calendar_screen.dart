import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart'; // Import table_calendar
import '../../data/providers/booking_provider.dart';
import '../../routes.dart'; // For navigation to booking form

class CalendarScreen extends StatefulWidget {
  final String villaId;

  const CalendarScreen({super.key, required this.villaId});

  static const String routeName = '/calendar';

  @override
  State<CalendarScreen> createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarScreen> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  RangeSelectionMode _rangeSelectionMode = RangeSelectionMode.toggledOn;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  DateTimeRange? _selectedDateRange;

  // Store fetched available dates as individual DateTime objects for easier lookup
  Set<DateTime> _availableDatesSet = {};
  Set<DateTime> _bookedDatesSet = {}; // To store booked dates
  bool _isLoadingAvailability = true;
  String? _availabilityError;

  @override
  void initState() {
    super.initState();
    // Fetch availability for the initial focused month
    _fetchVillaAvailability(month: _focusedDay.month, year: _focusedDay.year);
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Updated to take month and year
  Future<void> _fetchVillaAvailability({
    required int month,
    required int year,
  }) async {
    if (!mounted) return;
    setState(() {
      _isLoadingAvailability = true;
      _availabilityError = null;
    });

    final bookingProvider = Provider.of<BookingProvider>(
      context,
      listen: false,
    );

    // Call the updated fetchAvailability method
    await bookingProvider.fetchAvailability(widget.villaId, month, year);

    if (mounted) {
      if (bookingProvider.availabilityError != null) {
        _availabilityError = bookingProvider.availabilityError;
        _availableDatesSet = {}; // Clear on error
        _bookedDatesSet = {}; // Clear on error
      } else {
        _availableDatesSet.clear();
        for (var range in bookingProvider.availableDates) {
          for (int i = 0; i <= range.end.difference(range.start).inDays; i++) {
            _availableDatesSet.add(
              DateUtils.dateOnly(range.start.add(Duration(days: i))),
            );
          }
        }

        _bookedDatesSet.clear();
        for (var range in bookingProvider.bookedDates) {
          for (int i = 0; i <= range.end.difference(range.start).inDays; i++) {
            _bookedDatesSet.add(
              DateUtils.dateOnly(range.start.add(Duration(days: i))),
            );
          }
        }
      }
      setState(() {
        _isLoadingAvailability = false;
      });
    }
  }

  // Called by TableCalendar's onRangeSelected
  void _onDateRangeSelected(
    DateTime? start,
    DateTime? end,
    DateTime focusedDay,
  ) {
    setState(() {
      _selectedDay = null; // Clear single day selection
      _focusedDay = focusedDay;
      _selectedDateRange =
          (start != null && end != null)
              ? DateTimeRange(start: start, end: end)
              : null;

      // If the range selection is reset (e.g., user taps the start day again)
      if (start != null && end == null) {
        _rangeSelectionMode =
            RangeSelectionMode.toggledOn; // Keep range selection mode
      }
    });
  }

  void _navigateToBookingForm() {
    if (_selectedDateRange != null) {
      Navigator.of(context).pushNamed(
        AppRoutes.booking,
        arguments: {
          'villaId': widget.villaId,
          'fromDate': _selectedDateRange!.start,
          'toDate': _selectedDateRange!.end,
        },
      );
    }
  }

  // Helper for colored day circles in calendar
  Widget _buildDayCircle(DateTime day, Color color, {bool isBold = false}) {
    return Container(
      margin: const EdgeInsets.all(4.0),
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Center(
        child: Text(
          '${day.day}',
          style: TextStyle(
            color: Colors.white,
            fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
            fontSize: 13,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: _buildAppBar(context),
      body: Consumer<BookingProvider>(
        // Listen to BookingProvider for updates
        builder: (context, bookingProvider, child) {
          // Update local state based on provider state without setState during build
          if (!bookingProvider.isFetchingAvailability && _isLoadingAvailability) {
            // Use a microtask to update state after build is complete
            Future.microtask(() {
              if (mounted) {
                _availableDatesSet.clear();
                _bookedDatesSet.clear();

                if (bookingProvider.availabilityError == null) {
                  for (var range in bookingProvider.availableDates) {
                    for (int i = 0; i <= range.end.difference(range.start).inDays; i++) {
                      _availableDatesSet.add(
                        DateUtils.dateOnly(range.start.add(Duration(days: i))),
                      );
                    }
                  }
                  for (var range in bookingProvider.bookedDates) {
                    for (int i = 0; i <= range.end.difference(range.start).inDays; i++) {
                      _bookedDatesSet.add(
                        DateUtils.dateOnly(range.start.add(Duration(days: i))),
                      );
                    }
                  }
                } else {
                  _availabilityError = bookingProvider.availabilityError;
                }
                
                setState(() {
                  _isLoadingAvailability = false;
                });
              }
            });
          }

          // Use provider's loading state directly instead of local state
          final isCurrentlyLoading = bookingProvider.isFetchingAvailability || _isLoadingAvailability;

          return isCurrentlyLoading
              ? Center(
                  child: CircularProgressIndicator(
                    color: theme.colorScheme.primary,
                  ),
                )
              : Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildHeaderSection(context),
                          const SizedBox(height: 24),
                          if (_availabilityError != null)
                            _buildErrorCard(context),
                          _buildCalendarCard(context),
                          const SizedBox(height: 24),
                          _buildLegendSection(context),
                          const SizedBox(height: 16),
                          if (_selectedDateRange != null)
                            _buildSelectedDatesCard(context),
                          const SizedBox(height: 100), // Space for bottom button
                        ],
                      ),
                    ),
                  ),
                ],
              );
        },
      ),
      bottomNavigationBar: _buildBottomButton(context),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    return AppBar(
      elevation: 0,
      backgroundColor: theme.scaffoldBackgroundColor,
      foregroundColor: theme.colorScheme.onSurface,
      centerTitle: true,
      title: Text(
        'Select Booking Dates',
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.onSurface,
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios_new, size: 20),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withOpacity(0.1),
            theme.colorScheme.primary.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.calendar_today,
              color: theme.colorScheme.onPrimary,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Choose your dates',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Select your check-in and check-out dates for your stay',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorCard(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red.shade600),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Error fetching availability: $_availabilityError',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.red.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarCard(BuildContext context) {
    final theme = Theme.of(context);
    return Card(
      elevation: 4,
      shadowColor: theme.shadowColor.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: TableCalendar(
            firstDay: DateTime.now(),
            lastDay: DateTime.now().add(const Duration(days: 365)),
            focusedDay: _focusedDay,
            calendarFormat: _calendarFormat,
            rangeSelectionMode: _rangeSelectionMode,
            selectedDayPredicate: (day) =>
                _selectedDateRange != null &&
                isSameDay(_selectedDateRange!.start, day) &&
                isSameDay(_selectedDateRange!.end, day),
            rangeStartDay: _selectedDateRange?.start,
            rangeEndDay: _selectedDateRange?.end,
            enabledDayPredicate: (day) {
              return !day.isBefore(DateUtils.dateOnly(DateTime.now()));
            },
            onDaySelected: (selectedDay, focusedDay) {
              setState(() {
                _focusedDay = focusedDay;
                _selectedDay = selectedDay;

                final normalizedSelectedDay = DateUtils.dateOnly(selectedDay);
                if (_availableDatesSet.contains(normalizedSelectedDay)) {
                  _selectedDateRange = DateTimeRange(
                    start: normalizedSelectedDay,
                    end: normalizedSelectedDay,
                  );
                  _rangeSelectionMode = RangeSelectionMode.toggledOn;
                } else {
                  _selectedDateRange = null;
                }
              });
            },
            onRangeSelected: (DateTime? start, DateTime? end, DateTime focusedDay) {
              setState(() {
                _focusedDay = focusedDay;
                _selectedDay = null;

                if (start == null && end == null) {
                  _selectedDateRange = null;
                  _rangeSelectionMode = RangeSelectionMode.toggledOn;
                  return;
                }

                final DateTime normalizedStart = DateUtils.dateOnly(start!);

                if (end == null) {
                  if (_availableDatesSet.contains(normalizedStart)) {
                    _selectedDateRange = DateTimeRange(
                      start: normalizedStart,
                      end: normalizedStart,
                    );
                  } else {
                    _selectedDateRange = null;
                  }
                  _rangeSelectionMode = RangeSelectionMode.toggledOn;
                  return;
                }

                final DateTime normalizedEnd = DateUtils.dateOnly(end);

                if (normalizedStart.isAfter(normalizedEnd)) {
                  _selectedDateRange = null;
                  return;
                }

                bool allDatesInProposedRangeAvailable = true;
                DateTime currentDate = normalizedStart;
                while (!currentDate.isAfter(normalizedEnd)) {
                  if (!_availableDatesSet.contains(DateUtils.dateOnly(currentDate))) {
                    allDatesInProposedRangeAvailable = false;
                    break;
                  }
                  currentDate = currentDate.add(const Duration(days: 1));
                }

                if (allDatesInProposedRangeAvailable) {
                  _selectedDateRange = DateTimeRange(
                    start: normalizedStart,
                    end: normalizedEnd,
                  );
                } else {
                  if (_availableDatesSet.contains(normalizedStart)) {
                    _selectedDateRange = DateTimeRange(
                      start: normalizedStart,
                      end: normalizedStart,
                    );
                  } else {
                    _selectedDateRange = null;
                  }
                }
              });
            },
            onFormatChanged: (format) {
              if (_calendarFormat != format) {
                setState(() {
                  _calendarFormat = format;
                });
              }
            },
            onPageChanged: (focusedDay) {
              if (_focusedDay.month != focusedDay.month ||
                  _focusedDay.year != focusedDay.year) {
                setState(() {
                  _focusedDay = focusedDay;
                });
                _fetchVillaAvailability(
                  month: focusedDay.month,
                  year: focusedDay.year,
                );
              } else {
                _focusedDay = focusedDay;
              }
            },
            calendarBuilders: CalendarBuilders(
              defaultBuilder: (context, day, focusedDay) {
                final normalizedDay = DateUtils.dateOnly(day);
                if (_bookedDatesSet.contains(normalizedDay)) {
                  return _buildDayCircle(day, Colors.red.shade400);
                } else if (_availableDatesSet.contains(normalizedDay)) {
                  return _buildDayCircle(day, theme.colorScheme.primary.withOpacity(0.7));
                }
                return null;
              },
              rangeStartBuilder: (context, day, focusedDay) =>
                  _buildDayCircle(day, theme.colorScheme.primary, isBold: true),
              rangeEndBuilder: (context, day, focusedDay) =>
                  _buildDayCircle(day, theme.colorScheme.primary, isBold: true),
              withinRangeBuilder: (context, day, focusedDay) =>
                  _buildDayCircle(day, theme.colorScheme.primary.withOpacity(0.7)),
            ),
            calendarStyle: CalendarStyle(
              disabledTextStyle: TextStyle(color: Colors.grey[400]),
              outsideDaysVisible: false,
              weekendTextStyle: TextStyle(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
            headerStyle: HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              titleTextStyle: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ) ?? const TextStyle(),
              leftChevronIcon: Icon(
                Icons.chevron_left,
                color: theme.colorScheme.primary,
              ),
              rightChevronIcon: Icon(
                Icons.chevron_right,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLegendSection(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Legend',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildModernLegendItem(
                theme.colorScheme.primary.withOpacity(0.7),
                "Available",
                Icons.check_circle,
                theme,
              ),
              _buildModernLegendItem(
                Colors.red.shade400,
                "Booked",
                Icons.cancel,
                theme,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModernLegendItem(Color color, String text, IconData icon, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 6),
          Text(
            text,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedDatesCard(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withOpacity(0.1),
            theme.colorScheme.primary.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.event_available,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Selected Dates',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${DateFormat.yMMMd().format(_selectedDateRange!.start)} - ${DateFormat.yMMMd().format(_selectedDateRange!.end)}',
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface,
            ),
          ),
          if (_selectedDateRange!.start != _selectedDateRange!.end) ...[
            const SizedBox(height: 4),
            Text(
              '${_selectedDateRange!.end.difference(_selectedDateRange!.start).inDays + 1} ${_selectedDateRange!.end.difference(_selectedDateRange!.start).inDays == 0 ? 'night' : 'nights'}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBottomButton(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: ElevatedButton(
          onPressed: _selectedDateRange != null ? _navigateToBookingForm : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
            disabledBackgroundColor: theme.colorScheme.outline.withOpacity(0.3),
            disabledForegroundColor: theme.colorScheme.onSurface.withOpacity(0.4),
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
            elevation: 0,
            textStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Continue to Booking'),
              const SizedBox(width: 8),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: _selectedDateRange != null 
                    ? theme.colorScheme.onPrimary 
                    : theme.colorScheme.onSurface.withOpacity(0.4),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLegendItem(Color color, String text) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 8),
        Text(text),
      ],
    );
  }
}
