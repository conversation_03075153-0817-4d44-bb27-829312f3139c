import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../data/models/villa_model.dart';
import '../../data/providers/villa_provider.dart';
import '../../data/providers/user_provider.dart';
import '../../routes.dart';
import '../widgets/modern_villa_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize provider data when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final villaProvider = Provider.of<VillaProvider>(context, listen: false);
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // Set user provider reference for dynamic user ID
      villaProvider.setUserProvider(userProvider);

      // Initialize with dynamic parameters
      villaProvider.initialize();

      // Load current location address
      userProvider.loadCurrentLocationAddress();
    });
  }

  Future<void> _refreshData() async {
    final villaProvider = Provider.of<VillaProvider>(context, listen: false);
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    // Refresh both villa data and location address
    await Future.wait([
      villaProvider.refreshData(),
      userProvider.refreshLocationAddress(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _refreshData,
          child: Consumer2<VillaProvider, UserProvider>(
            builder: (context, villaProvider, userProvider, child) {
              final featuredVillas = villaProvider.featuredVillas;
              final nearbyVillas = villaProvider.nearbyVillas;

              return CustomScrollView(
                slivers: [
                  // App Bar
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header with title and notification icon
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Luxury Villas',
                                      style: TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        const Icon(
                                          Icons.location_on,
                                          size: 16,
                                          color: Colors.grey,
                                        ),
                                        const SizedBox(width: 4),
                                        if (userProvider.isLoadingLocation)
                                          SizedBox(
                                            width: 12,
                                            height: 12,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              color: Colors.grey[600],
                                            ),
                                          )
                                        else
                                          Expanded(
                                            child: Text(
                                              userProvider
                                                  .currentLocationAddress,
                                              style: TextStyle(
                                                color: Colors.grey[600],
                                                fontSize: 14,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              Row(
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.category),
                                    onPressed: () {
                                      Navigator.of(
                                        context,
                                      ).pushNamed(AppRoutes.categories);
                                    },
                                    tooltip: 'Categories',
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.history),
                                    onPressed: () {
                                      Navigator.of(context).push(
                                        AppRoutes.generateBookingHistoryRoute(
                                          context,
                                        ),
                                      );
                                    },
                                    tooltip: 'Booking History',
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Search bar (now just for display, navigation handled by bottom nav)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(30),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.search, color: Colors.grey),
                                const SizedBox(width: 8),
                                Text(
                                  'Search villas, locations...',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Category filters
                          SizedBox(
                            height: 40,
                            child:
                                villaProvider.isCategoriesLoading
                                    ? const Center(
                                      child: SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      ),
                                    )
                                    : ListView.builder(
                                      scrollDirection: Axis.horizontal,
                                      itemCount:
                                          villaProvider.categories.length,
                                      itemBuilder: (context, index) {
                                        final category =
                                            villaProvider.categories[index];
                                        final isSelected =
                                            category ==
                                            villaProvider.selectedCategory;
                                        return Padding(
                                          padding: const EdgeInsets.only(
                                            right: 8,
                                          ),
                                          child: FilterChip(
                                            label: Text(category.name),
                                            selected: isSelected,
                                            onSelected: (selected) {
                                              villaProvider.selectCategory(
                                                category,
                                              );
                                            },
                                            backgroundColor: Colors.white,
                                            selectedColor: Colors.blue,
                                            checkmarkColor: Colors.white,
                                            labelStyle: TextStyle(
                                              color:
                                                  isSelected
                                                      ? Colors.white
                                                      : Colors.black,
                                              fontWeight:
                                                  isSelected
                                                      ? FontWeight.bold
                                                      : FontWeight.normal,
                                            ),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                              side: BorderSide(
                                                color:
                                                    isSelected
                                                        ? Colors.blue
                                                        : Colors.grey[300]!,
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Featured Villas Section - Conditionally render if not empty
                  if (featuredVillas.isNotEmpty)
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Featured Villas',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    // TODO: Navigate to all featured villas
                                  },
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        'View all',
                                        style: TextStyle(
                                          color: Colors.blue[700],
                                          fontSize: 14,
                                        ),
                                      ),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        size: 12,
                                        color: Colors.blue[700],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Container(
                              constraints: const BoxConstraints(maxHeight: 200),
                              child:
                                  villaProvider.isLoading
                                      ? const Center(
                                        child: CircularProgressIndicator(),
                                      )
                                      : villaProvider.error != null
                                      ? Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.error_outline,
                                              color: Colors.red[400],
                                              size: 48,
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              'Failed to load villas',
                                              style: TextStyle(
                                                color: Colors.red[400],
                                                fontSize: 16,
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                            ElevatedButton(
                                              onPressed: () {
                                                villaProvider.refreshData();
                                              },
                                              child: const Text('Retry'),
                                            ),
                                          ],
                                        ),
                                      )
                                      // No need for featuredVillas.isEmpty check here as the parent SliverToBoxAdapter is conditional
                                      : ListView.builder(
                                        scrollDirection: Axis.horizontal,
                                        itemCount: featuredVillas.length,
                                        padding: EdgeInsets.zero,
                                        itemBuilder: (context, index) {
                                          return Container(
                                            width: 280,
                                            margin: const EdgeInsets.only(
                                              right: 16,
                                            ),
                                            child: ModernVillaCard(
                                              villa: featuredVillas[index],
                                              onTap: () {
                                                Navigator.of(context).push(
                                                  AppRoutes.generateVillaDetailRoute(
                                                    context,
                                                    featuredVillas[index].id,
                                                  ),
                                                );
                                              },
                                            ),
                                          );
                                        },
                                      ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (featuredVillas.isEmpty &&
                      !villaProvider.isLoading &&
                      villaProvider.error == null)
                    const SliverToBoxAdapter(
                      child: SizedBox.shrink(),
                    ), // Effectively hides the section
                  // Nearby Villas Section
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Nearby Villas',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  // TODO: Navigate to all nearby villas
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      'View all',
                                      style: TextStyle(
                                        color: Colors.blue[700],
                                        fontSize: 14,
                                      ),
                                    ),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      size: 12,
                                      color: Colors.blue[700],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          villaProvider.isLoading
                              ? const Center(
                                child: Padding(
                                  padding: EdgeInsets.all(32.0),
                                  child: CircularProgressIndicator(),
                                ),
                              )
                              : villaProvider.error != null
                              ? Center(
                                child: Padding(
                                  padding: const EdgeInsets.all(32.0),
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.error_outline,
                                        color: Colors.red[400],
                                        size: 48,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Failed to load nearby villas',
                                        style: TextStyle(
                                          color: Colors.red[400],
                                          fontSize: 16,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      ElevatedButton(
                                        onPressed: () {
                                          villaProvider.refreshData();
                                        },
                                        child: const Text('Retry'),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                              : nearbyVillas.isEmpty
                              ? const Center(
                                child: Padding(
                                  padding: EdgeInsets.all(32.0),
                                  child: Text(
                                    'No nearby villas found',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ),
                              )
                              : ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemCount: nearbyVillas.length,
                                itemBuilder: (context, index) {
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                    ),
                                    child: ModernVillaCard(
                                      villa: nearbyVillas[index],
                                      showFullDetails: true,
                                      onTap: () {
                                        Navigator.of(context).push(
                                          AppRoutes.generateVillaDetailRoute(
                                            context,
                                            nearbyVillas[index].id,
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                },
                              ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedVillaCard(Villa villa) {
    return GestureDetector(
      onTap: () {
        // Navigate to villa detail screen
        Navigator.of(
          context,
        ).push(AppRoutes.generateVillaDetailRoute(context, villa.id));
      },
      child: Container(
        width: 280,
        height: 200, // Fixed height to match the parent constraint
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13), // 0.05 opacity
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Villa image with bookmark icon - using Expanded to take remaining space
              Expanded(
                child: Stack(
                  children: [
                    Image.network(
                      villa.imageUrl,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[300],
                          child: const Center(
                            child: Icon(
                              Icons.image_not_supported,
                              size: 40,
                              color: Colors.grey,
                            ),
                          ),
                        );
                      },
                    ),
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(26), // 0.1 opacity
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Consumer<VillaProvider>(
                          builder: (context, provider, child) {
                            final isSaved = provider.isVillaSaved(villa.id);
                            return IconButton(
                              icon: Icon(
                                isSaved
                                    ? Icons.bookmark
                                    : Icons.bookmark_border,
                                color: isSaved ? Colors.blue : null,
                              ),
                              onPressed: () async {
                                await provider.toggleSavedStatus(villa);
                              },
                              constraints: const BoxConstraints(
                                minHeight: 32,
                                minWidth: 32,
                              ),
                              padding: EdgeInsets.zero,
                              iconSize: 16,
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Villa details - using a fixed height container
              Container(
                height: 70, // Fixed height for details section
                padding: const EdgeInsets.symmetric(
                  horizontal: 8.0,
                  vertical: 4.0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Villa name
                    Text(
                      villa.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    // Location
                    Row(
                      children: [
                        const Icon(
                          Icons.location_on,
                          color: Colors.grey,
                          size: 12,
                        ),
                        const SizedBox(width: 2),
                        Expanded(
                          child: Text(
                            villa.location,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    // Rating and price
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.star,
                                color: Colors.amber,
                                size: 12,
                              ),
                              const SizedBox(width: 2),
                              Text(
                                villa.rating.toString(),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          Text(
                            '₹${villa.price.toInt()}/night',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNearbyVillaCard(Villa villa) {
    return GestureDetector(
      onTap: () {
        // Navigate to villa detail screen
        Navigator.of(
          context,
        ).push(AppRoutes.generateVillaDetailRoute(context, villa.id));
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Villa image
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    villa.imageUrl,
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 100,
                        height: 100,
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.image_not_supported,
                          size: 40,
                          color: Colors.grey,
                        ),
                      );
                    },
                  ),
                ),
                Positioned(
                  top: 2,
                  right: 2,
                  child: Container(
                    height: 20,
                    width: 20,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(26), // 0.1 opacity
                          blurRadius: 1,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Consumer<VillaProvider>(
                      builder: (context, provider, child) {
                        final isSaved = provider.isVillaSaved(villa.id);
                        return InkWell(
                          onTap: () async {
                            await provider.toggleSavedStatus(villa);
                          },
                          child: Center(
                            child: Icon(
                              isSaved ? Icons.bookmark : Icons.bookmark_border,
                              color: isSaved ? Colors.blue : null,
                              size: 12,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(width: 12),
            // Villa details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    villa.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        color: Colors.grey,
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          '${villa.distanceInMiles} miles away',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.star, color: Colors.amber, size: 14),
                      const SizedBox(width: 4),
                      Text(
                        villa.rating.toString(),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '₹${villa.price.toInt()}/night',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
