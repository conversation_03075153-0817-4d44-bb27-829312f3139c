class Amenity {
  final int id;
  final bool enabled;
  final bool isDeleted;
  final String title;
  final String? icon;
  final int priority;
  final int? createdBy;
  final DateTime? createdTimestamp;
  final int? updatedBy;
  final DateTime? updatedTimestamp;

  Amenity({
    required this.id,
    required this.enabled,
    required this.isDeleted,
    required this.title,
    this.icon,
    required this.priority,
    this.createdBy,
    this.createdTimestamp,
    this.updatedBy,
    this.updatedTimestamp,
  });

  factory Amenity.fromJson(Map<String, dynamic> json) {
    return Amenity(
      id: _parseId(json['id']),
      enabled: _parseBool(json['enabled']),
      isDeleted: _parseBool(json['isDeleted']),
      title: json['title']?.toString() ?? '',
      icon: json['icon']?.toString(),
      priority: _parseId(json['priority']),
      createdBy: json['createdBy'] != null ? _parseId(json['createdBy']) : null,
      createdTimestamp: json['createdTimestamp'] != null
          ? DateTime.tryParse(json['createdTimestamp'].toString())
          : null,
      updatedBy: json['updatedBy'] != null ? _parseId(json['updatedBy']) : null,
      updatedTimestamp: json['updatedTimestamp'] != null
          ? DateTime.tryParse(json['updatedTimestamp'].toString())
          : null,
    );
  }

  // Helper method to parse ID from various types
  static int _parseId(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) {
      try {
        return int.parse(value);
      } catch (e) {
        return 0;
      }
    }
    return 0;
  }

  // Helper method to parse boolean from various types
  static bool _parseBool(dynamic value) {
    if (value == null) return false;
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true' || value == '1';
    }
    if (value is int) return value == 1;
    return false;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'enabled': enabled,
      'isDeleted': isDeleted,
      'title': title,
      'icon': icon,
      'priority': priority,
      'createdBy': createdBy,
      'createdTimestamp': createdTimestamp?.toIso8601String(),
      'updatedBy': updatedBy,
      'updatedTimestamp': updatedTimestamp?.toIso8601String(),
    };
  }

  // Convenience getters
  bool get isActive => enabled && !isDeleted;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Amenity && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Amenity(id: $id, title: $title, enabled: $enabled)';
}
