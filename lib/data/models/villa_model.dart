import '../models/base_model.dart';

class Villa extends BaseModel {
  final String id;
  final String name;
  final String location;
  final String imageUrl;
  final double rating;
  final double price;
  final String? description;
  final double? distanceInMiles;
  final List<String>? amenities;
  final List<String>? additionalImages;
  final bool isFeatured;
  final double? latitude;
  final double? longitude;
  final int? villaGroupId;

  Villa({
    required this.id,
    required this.name,
    required this.location,
    required this.imageUrl,
    required this.rating,
    required this.price,
    this.description,
    this.distanceInMiles,
    this.amenities,
    this.additionalImages,
    this.isFeatured = false,
    this.latitude,
    this.longitude,
    this.villaGroupId,
  });

  factory Villa.fromJson(Map<String, dynamic> json) {
    return Villa(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      location: json['location']?.toString() ?? '',
      imageUrl: json['imageUrl']?.toString() ?? '',
      rating: _parseDouble(json['rating']) ?? 0.0,
      price: _parseDouble(json['price']) ?? 0.0,
      description: json['description']?.toString(),
      distanceInMiles: _parseDouble(json['distanceInMiles']),
      amenities: _parseStringList(json['amenities']),
      additionalImages: _parseStringList(json['additionalImages']),
      isFeatured: _parseBool(json['isFeatured']) ?? false,
      latitude: _parseDouble(json['latitude']),
      longitude: _parseDouble(json['longitude']),
      villaGroupId: _parseInt(json['villaGroupId']),
    );
  }

  // Helper method to parse double from various types
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Helper method to parse boolean from various types
  static bool? _parseBool(dynamic value) {
    if (value == null) return null;
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true' || value == '1';
    }
    if (value is int) return value == 1;
    return null;
  }

  // Helper method to parse int from various types
  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      try {
        return int.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Helper method to parse string list from various types
  static List<String>? _parseStringList(dynamic value) {
    if (value == null) return null;
    if (value is List) {
      try {
        return value.map((item) => item.toString()).toList();
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'location': location,
      'imageUrl': imageUrl,
      'rating': rating,
      'price': price,
      'description': description,
      'distanceInMiles': distanceInMiles,
      'amenities': amenities,
      'additionalImages': additionalImages,
      'isFeatured': isFeatured,
      'latitude': latitude,
      'longitude': longitude,
      'villaGroupId': villaGroupId,
    };
  }

  @override
  List<Object?> get props => [
    id,
    name,
    location,
    imageUrl,
    rating,
    price,
    description,
    distanceInMiles,
    amenities,
    additionalImages,
    isFeatured,
    latitude,
    longitude,
    villaGroupId,
  ];
}
