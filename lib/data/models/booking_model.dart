import '../models/base_model.dart';

class BookingModel extends BaseModel {
  final String villaId;
  final DateTime? checkInDate;
  final int? status;
  final double? amount;
  final double? gst;
  final double? totalAmount;
  final int? noOfDay;
  final int? noOfAdult;
  final int? bookingType;
  final int? priority;
  final DateTime? checkOutDate;
  final int numberOfGuests;
  final double pricePerNight;
  final int numberOfNights;
  final double cleaningFee;
  final double serviceFee;
  final double totalPrice;
  final int? numberOfChildren;
  final String? paymentMode;
  final String? paymentStatus;
  final String? remark;


  BookingModel({
    required this.villaId,
    this.checkInDate,
    this.checkOutDate,
    this.numberOfGuests = 2,
    required this.pricePerNight,
    this.numberOfNights = 2,
    this.cleaningFee = 50.0,
    this.serviceFee = 35.0,
    double? totalPrice,
    this.numberOfChildren,
    this.paymentMode,
    this.paymentStatus,
    this.remark,
    this.status,
    this.amount,
    this.gst,
    this.totalAmount,
    this.noOfDay,
    this.noOfAdult,
    this.bookingType,
    this.priority,
  }) : totalPrice = totalPrice ?? (pricePerNight * numberOfNights + cleaningFee + serviceFee);

  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      villaId: json['villaId'],
      checkInDate: json['checkInDate'] != null ? DateTime.parse(json['checkInDate']) : null,
      checkOutDate: json['checkOutDate'] != null ? DateTime.parse(json['checkOutDate']) : null,
      numberOfGuests: json['numberOfGuests'] ?? 2,
      pricePerNight: json['pricePerNight'].toDouble(),
      numberOfNights: json['numberOfNights'] ?? 2,
      cleaningFee: json['cleaningFee']?.toDouble() ?? 50.0,
      serviceFee: json['serviceFee']?.toDouble() ?? 35.0,
      totalPrice: json['totalPrice']?.toDouble(),
      numberOfChildren: json['numberOfChildren'] as int?,
      paymentMode: json['paymentMode'] as String?,
      paymentStatus: json['paymentStatus'] as String?,
      remark: json['remark'] as String?,
      status: json['status'] as int?,
      amount: json['amount']?.toDouble(),
      gst: json['gst']?.toDouble(),
      totalAmount: json['totalAmount']?.toDouble(),
      noOfDay: json['noOfDay'] as int?,
      noOfAdult: json['noOfAdult'] as int?,
      bookingType: json['bookingType'] as int?,
      priority: json['priority'] as int?,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'villaId': villaId,
      'checkInDate': checkInDate?.toIso8601String(),
      'checkOutDate': checkOutDate?.toIso8601String(),
      'numberOfGuests': numberOfGuests,
      'pricePerNight': pricePerNight,
      'numberOfNights': numberOfNights,
      'cleaningFee': cleaningFee,
      'serviceFee': serviceFee,
      'totalPrice': totalPrice,
      if (numberOfChildren != null) 'numberOfChildren': numberOfChildren,
      if (paymentMode != null) 'paymentMode': paymentMode,
      if (paymentStatus != null) 'paymentStatus': paymentStatus,
      if (remark != null) 'remark': remark,
      if (status != null) 'status': status,
      if (amount != null) 'amount': amount,
      if (gst != null) 'gst': gst,
      if (totalAmount != null) 'totalAmount': totalAmount,
      if (noOfDay != null) 'noOfDay': noOfDay,
      if (noOfAdult != null) 'noOfAdult': noOfAdult,
      if (bookingType != null) 'bookingType': bookingType,
      if (priority != null) 'priority': priority,
    };
  }

  BookingModel copyWith({
    String? villaId,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? numberOfGuests,
    double? pricePerNight,
    int? numberOfNights,
    double? cleaningFee,
    double? serviceFee,
    double? totalPrice,
    int? numberOfChildren,
    String? paymentMode,
    String? paymentStatus,
    String? remark,
    int? status,
    double? amount,
    double? gst,
    double? totalAmount,
    int? noOfDay,
    int? noOfAdult,
    int? bookingType,
    int? priority,
  }) {
    return BookingModel(
      villaId: villaId ?? this.villaId,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      numberOfGuests: numberOfGuests ?? this.numberOfGuests,
      pricePerNight: pricePerNight ?? this.pricePerNight,
      numberOfNights: numberOfNights ?? this.numberOfNights,
      cleaningFee: cleaningFee ?? this.cleaningFee,
      serviceFee: serviceFee ?? this.serviceFee,
      totalPrice: totalPrice ?? this.totalPrice,
      numberOfChildren: numberOfChildren ?? this.numberOfChildren,
      paymentMode: paymentMode ?? this.paymentMode,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      remark: remark ?? this.remark,
      status: status ?? this.status,
      amount: amount ?? this.amount,
      gst: gst ?? this.gst,
      totalAmount: totalAmount ?? this.totalAmount,
      noOfDay: noOfDay ?? this.noOfDay,
      noOfAdult: noOfAdult ?? this.noOfAdult,
      bookingType: bookingType ?? this.bookingType,
      priority: priority ?? this.priority,
    );
  }

  @override
  List<Object?> get props => [
        villaId,
        checkInDate,
        checkOutDate,
        numberOfGuests,
        pricePerNight,
        numberOfNights,
        cleaningFee,
        serviceFee,
        totalPrice,
        numberOfChildren,
        paymentMode,
        paymentStatus,
        remark,
      ];
}
