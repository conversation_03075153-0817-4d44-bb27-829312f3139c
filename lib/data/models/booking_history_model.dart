import 'package:flutter/material.dart';
import '../models/base_model.dart';

enum BookingStatus { upcoming, completed, cancelled }

enum BookingType { hotel, flight, villa, car, cruise }

class BookingHistoryModel extends BaseModel {
  final String id;
  final BookingType type;
  final BookingStatus status;
  final DateTime startDate;
  final DateTime endDate;
  final String location;
  final String imageUrl;
  final double price;
  final String? description;

  BookingHistoryModel({
    required this.id,
    required this.type,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.location,
    required this.imageUrl,
    required this.price,
    this.description,
  });

  factory BookingHistoryModel.fromJson(Map<String, dynamic> json) {
    return BookingHistoryModel(
      id: json['id'],
      type: BookingType.values.firstWhere(
        (e) => e.toString() == 'BookingType.${json['type']}',
        orElse: () => BookingType.hotel,
      ),
      status: BookingStatus.values.firstWhere(
        (e) => e.toString() == 'BookingStatus.${json['status']}',
        orElse: () => BookingStatus.upcoming,
      ),
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      location: json['location'],
      imageUrl: json['imageUrl'],
      price: json['price'].toDouble(),
      description: json['description'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'location': location,
      'imageUrl': imageUrl,
      'price': price,
      'description': description,
    };
  }

  String get formattedDateRange {
    final startMonth = _getMonthAbbreviation(startDate.month);
    final endMonth = _getMonthAbbreviation(endDate.month);

    if (startDate.month == endDate.month && startDate.year == endDate.year) {
      return '$startMonth ${startDate.day} - ${endDate.day}, ${startDate.year}';
    } else {
      return '$startMonth ${startDate.day} - $endMonth ${endDate.day}, ${endDate.year}';
    }
  }

  String _getMonthAbbreviation(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return months[month - 1];
  }

  String get typeDisplayName {
    switch (type) {
      case BookingType.hotel:
        return 'Hotel';
      case BookingType.flight:
        return 'Flight';
      case BookingType.villa:
        return 'Villa';
      case BookingType.car:
        return 'Car';
      case BookingType.cruise:
        return 'Cruise';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case BookingStatus.upcoming:
        return 'Upcoming';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.cancelled:
        return 'Cancelled';
    }
  }

  Color getStatusColor() {
    switch (status) {
      case BookingStatus.upcoming:
        return const Color(0xFF1E9D6A); // Green
      case BookingStatus.completed:
        return const Color(0xFF2F80ED); // Blue
      case BookingStatus.cancelled:
        return const Color(0xFFEB5757); // Red
    }
  }

  @override
  List<Object?> get props => [
    id,
    type,
    status,
    startDate,
    endDate,
    location,
    imageUrl,
    price,
    description,
  ];
}
