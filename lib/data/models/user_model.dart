import '../models/base_model.dart';

class UserModel extends BaseModel {
  final int id;
  final bool enabled;
  final bool isDeleted;
  final String name;
  final String dialCode;
  final String countryCode;
  final String contactNumber;
  final String email;
  final String gender;
  final String dob;
  final String? googleAuthToken;
  final String? appleAuthToken;
  final String? profilePicture;
  final bool isVerified;
  final String? createdBy;
  final String? createdTimestamp;
  final String? updatedBy;
  final String? updatedTimestamp;
  final String? token; // Auth token from API response

  UserModel({
    required this.id,
    required this.enabled,
    required this.isDeleted,
    required this.name,
    required this.dialCode,
    required this.countryCode,
    required this.contactNumber,
    required this.email,
    required this.gender,
    required this.dob,
    this.googleAuthToken,
    this.appleAuthToken,
    this.profilePicture,
    required this.isVerified,
    this.createdBy,
    this.createdTimestamp,
    this.updatedBy,
    this.updatedTimestamp,
    this.token,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? 0,
      enabled: json['enabled'] ?? true,
      isDeleted: json['isDeleted'] ?? false,
      name: json['name'] ?? '',
      dialCode: json['dialCode'] ?? '+91',
      countryCode: json['countryCode'] ?? 'IN',
      contactNumber: json['contactNumber'] ?? '',
      email: json['email'] ?? '',
      gender: json['gender'] ?? '',
      dob: json['dob'] ?? '',
      googleAuthToken: json['googleAuthToken'],
      appleAuthToken: json['appleAuthToken'],
      profilePicture: json['profilePicture'],
      isVerified: json['isVerified'] ?? false,
      createdBy: json['createdBy'],
      createdTimestamp: json['createdTimestamp'],
      updatedBy: json['updatedBy'],
      updatedTimestamp: json['updatedTimestamp'],
      token: json['token'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'enabled': enabled,
      'isDeleted': isDeleted,
      'name': name,
      'dialCode': dialCode,
      'countryCode': countryCode,
      'contactNumber': contactNumber,
      'email': email,
      'gender': gender,
      'dob': dob,
      'googleAuthToken': googleAuthToken,
      'appleAuthToken': appleAuthToken,
      'profilePicture': profilePicture,
      'isVerified': isVerified,
      'createdBy': createdBy,
      'createdTimestamp': createdTimestamp,
      'updatedBy': updatedBy,
      'updatedTimestamp': updatedTimestamp,
      'token': token,
    };
  }

  UserModel copyWith({
    int? id,
    bool? enabled,
    bool? isDeleted,
    String? name,
    String? dialCode,
    String? countryCode,
    String? contactNumber,
    String? email,
    String? gender,
    String? dob,
    String? googleAuthToken,
    String? appleAuthToken,
    String? profilePicture,
    bool? isVerified,
    String? createdBy,
    String? createdTimestamp,
    String? updatedBy,
    String? updatedTimestamp,
    String? token,
  }) {
    return UserModel(
      id: id ?? this.id,
      enabled: enabled ?? this.enabled,
      isDeleted: isDeleted ?? this.isDeleted,
      name: name ?? this.name,
      dialCode: dialCode ?? this.dialCode,
      countryCode: countryCode ?? this.countryCode,
      contactNumber: contactNumber ?? this.contactNumber,
      email: email ?? this.email,
      gender: gender ?? this.gender,
      dob: dob ?? this.dob,
      googleAuthToken: googleAuthToken ?? this.googleAuthToken,
      appleAuthToken: appleAuthToken ?? this.appleAuthToken,
      profilePicture: profilePicture ?? this.profilePicture,
      isVerified: isVerified ?? this.isVerified,
      createdBy: createdBy ?? this.createdBy,
      createdTimestamp: createdTimestamp ?? this.createdTimestamp,
      updatedBy: updatedBy ?? this.updatedBy,
      updatedTimestamp: updatedTimestamp ?? this.updatedTimestamp,
      token: token ?? this.token,
    );
  }

  @override
  List<Object?> get props => [
        id,
        enabled,
        isDeleted,
        name,
        dialCode,
        countryCode,
        contactNumber,
        email,
        gender,
        dob,
        googleAuthToken,
        appleAuthToken,
        profilePicture,
        isVerified,
        createdBy,
        createdTimestamp,
        updatedBy,
        updatedTimestamp,
        token,
      ];
}
