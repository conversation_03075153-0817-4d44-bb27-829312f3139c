import 'api_villa_model.dart';
import 'user_model.dart';

class WishlistItem {
  final int id;
  final bool enabled;
  final bool isDeleted;
  final int userId;
  final int villaId;
  final int priority;
  final int createdBy;
  final String createdTimestamp;
  final int updatedBy;
  final String updatedTimestamp;
  final ApiVilla? villa;
  final UserModel? user;

  WishlistItem({
    required this.id,
    required this.enabled,
    required this.isDeleted,
    required this.userId,
    required this.villaId,
    required this.priority,
    required this.createdBy,
    required this.createdTimestamp,
    required this.updatedBy,
    required this.updatedTimestamp,
    this.villa,
    this.user,
  });

  factory WishlistItem.fromJson(Map<String, dynamic> json) {
    return WishlistItem(
      id: json['id'] as int? ?? 0,
      enabled: json['enabled'] as bool? ?? true,
      isDeleted: json['isDeleted'] as bool? ?? false,
      userId: json['userId'] as int? ?? 0,
      villaId: json['villaId'] as int? ?? 0,
      priority: json['priority'] as int? ?? 0,
      createdBy: json['createdBy'] as int? ?? 0,
      createdTimestamp: json['createdTimestamp'] as String? ?? '',
      updatedBy: json['updatedBy'] as int? ?? 0,
      updatedTimestamp: json['updatedTimestamp'] as String? ?? '',
      villa: json['villa'] != null 
          ? ApiVilla.fromJsonSafe(json['villa'] as Map<String, dynamic>)
          : null,
      user: json['user'] != null 
          ? UserModel.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'enabled': enabled,
      'isDeleted': isDeleted,
      'userId': userId,
      'villaId': villaId,
      'priority': priority,
      'createdBy': createdBy,
      'createdTimestamp': createdTimestamp,
      'updatedBy': updatedBy,
      'updatedTimestamp': updatedTimestamp,
      'villa': villa?.toJson(),
      'user': user?.toJson(),
    };
  }

  /// Safe factory method to handle potential parsing errors
  static WishlistItem? fromJsonSafe(Map<String, dynamic> json) {
    try {
      return WishlistItem.fromJson(json);
    } catch (e) {
      // Log error if needed
      return null;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WishlistItem &&
        other.id == id &&
        other.villaId == villaId &&
        other.userId == userId;
  }

  @override
  int get hashCode => id.hashCode ^ villaId.hashCode ^ userId.hashCode;
}

class AddToWishlistRequest {
  final int villaId;
  final int userId;

  AddToWishlistRequest({
    required this.villaId,
    required this.userId,
  });

  Map<String, dynamic> toJson() {
    return {
      'villaId': villaId,
      'userId': userId,
    };
  }
}

class RemoveFromWishlistRequest {
  final int wishlistId;

  RemoveFromWishlistRequest({
    required this.wishlistId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': wishlistId,
    };
  }
}