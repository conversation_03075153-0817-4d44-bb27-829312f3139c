import '../models/base_model.dart';

enum MessageType {
  text,
  image,
  file,
  location,
  booking,
}

class ChatMessage extends BaseModel {
  final String id;
  final String senderId;
  final String? senderName;
  final String? senderAvatar;
  final String conversationId;
  final String content;
  final DateTime timestamp;
  final bool isRead;
  final MessageType type;
  final Map<String, dynamic>? metadata;

  ChatMessage({
    required this.id,
    required this.senderId,
    this.senderName,
    this.senderAvatar,
    required this.conversationId,
    required this.content,
    required this.timestamp,
    this.isRead = false,
    this.type = MessageType.text,
    this.metadata,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      senderId: json['senderId'],
      senderName: json['senderName'],
      senderAvatar: json['senderAvatar'],
      conversationId: json['conversationId'],
      content: json['content'],
      timestamp: DateTime.parse(json['timestamp']),
      isRead: json['isRead'] ?? false,
      type: MessageType.values.firstWhere(
        (e) => e.toString() == 'MessageType.${json['type']}',
        orElse: () => MessageType.text,
      ),
      metadata: json['metadata'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'senderAvatar': senderAvatar,
      'conversationId': conversationId,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'type': type.toString().split('.').last,
      'metadata': metadata,
    };
  }

  @override
  List<Object?> get props => [
        id,
        senderId,
        senderName,
        senderAvatar,
        conversationId,
        content,
        timestamp,
        isRead,
        type,
        metadata,
      ];
}

class BookingInfo {
  final String villaId;
  final String villaName;
  final String villaImage;
  final DateTime checkInDate;
  final DateTime checkOutDate;

  BookingInfo({
    required this.villaId,
    required this.villaName,
    required this.villaImage,
    required this.checkInDate,
    required this.checkOutDate,
  });

  factory BookingInfo.fromJson(Map<String, dynamic> json) {
    return BookingInfo(
      villaId: json['villaId'],
      villaName: json['villaName'],
      villaImage: json['villaImage'],
      checkInDate: DateTime.parse(json['checkInDate']),
      checkOutDate: DateTime.parse(json['checkOutDate']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'villaId': villaId,
      'villaName': villaName,
      'villaImage': villaImage,
      'checkInDate': checkInDate.toIso8601String(),
      'checkOutDate': checkOutDate.toIso8601String(),
    };
  }
}
