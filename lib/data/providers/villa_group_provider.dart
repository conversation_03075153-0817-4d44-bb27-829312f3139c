import 'package:flutter/material.dart';
import '../models/villa_model.dart';
import '../models/api_villa_model.dart';
import '../datasources/api_service.dart';
import '../../core/utils/service_locator.dart';

class VillaGroupProvider extends ChangeNotifier {
  final ApiService _apiService = sl.apiService;

  // State variables
  List<Villa> _villaGroupVillas = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Villa> get villaGroupVillas => _villaGroupVillas;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Fetch villa group list from API
  Future<void> fetchVillaGroupList({
    required String fromDate,
    required String toDate,
    required int villaGroupId,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      sl.logger.d(
        'VillaGroupProvider: Fetching villa group list for group ID: $villaGroupId',
      );
      sl.logger.d('VillaGroupProvider: Date range: $fromDate to $toDate');

      final response = await _apiService.getVillaGroupList(
        fromDate: fromDate,
        toDate: toDate,
        villaGroupId: villaGroupId,
      );

      sl.logger.d('VillaGroupProvider: Villa group API response: $response');
      sl.logger.d('VillaGroupProvider: Response type: ${response.runtimeType}');

      if (response != null) {
        List<dynamic> villaList;

        // Handle different response formats
        if (response is Map<String, dynamic>) {
          if (response.containsKey('data') && response['data'] is List) {
            villaList = response['data'] as List<dynamic>;
          } else if (response.containsKey('villas') &&
              response['villas'] is List) {
            villaList = response['villas'] as List<dynamic>;
          } else if (response.containsKey('result') &&
              response['result'] is List) {
            villaList = response['result'] as List<dynamic>;
          } else {
            // If response is a map but doesn't contain expected keys, treat as single item
            villaList = [response];
          }
        } else if (response is List) {
          villaList = response;
        } else {
          throw Exception(
            'Unexpected response format: ${response.runtimeType}',
          );
        }

        sl.logger.d(
          'VillaGroupProvider: Processing ${villaList.length} villas from villa group response',
        );

        // Store previous count for comparison
        final previousCount = _villaGroupVillas.length;

        // Convert API villas to Villa objects
        _villaGroupVillas =
            villaList
                .map((json) {
                  try {
                    if (json is! Map<String, dynamic>) {
                      sl.logger.w(
                        'VillaGroupProvider: Villa JSON is not a Map: ${json.runtimeType} - $json',
                      );
                      return null;
                    }

                    // Parse as ApiVilla first, then convert to Villa
                    final apiVilla = ApiVilla.fromJsonSafe(json);
                    return _convertApiVillaToVilla(apiVilla);
                  } catch (e, stackTrace) {
                    sl.logger.e(
                      'VillaGroupProvider: Failed to parse villa from villa group:',
                    );
                    sl.logger.e('Villa data: $json');
                    sl.logger.e('Error: $e');
                    sl.logger.e('Stack trace: $stackTrace');
                    return null;
                  }
                })
                .where((villa) => villa != null)
                .cast<Villa>()
                .toList();

        sl.logger.d(
          'VillaGroupProvider: Successfully parsed ${_villaGroupVillas.length} villas from villa group (previous: $previousCount)',
        );
      } else {
        throw Exception('Empty response from server');
      }
    } catch (e) {
      sl.logger.e('Error fetching villa group list: $e');
      _error = e.toString();
      _villaGroupVillas = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Convert ApiVilla to Villa for consistency
  Villa? _convertApiVillaToVilla(ApiVilla apiVilla) {
    try {
      // Convert boolean amenities to string list
      List<String> amenities = [];

      // Add amenities based on boolean flags
      if (apiVilla.wifi) amenities.add('WiFi');
      if (apiVilla.ac) amenities.add('AC');
      if (apiVilla.parking) amenities.add('Parking');
      if (apiVilla.kitchen) amenities.add('Kitchen');
      if (apiVilla.isSwimmingPool) amenities.add('Swimming Pool');
      if (apiVilla.barbeque) amenities.add('Barbeque');
      if (apiVilla.petFriendly) amenities.add('Pet Friendly');
      if (apiVilla.fireExtinguisher) amenities.add('Fire Extinguisher');
      if (apiVilla.powerBackup) amenities.add('Power Backup');

      return Villa(
        id: apiVilla.id.toString(),
        name: apiVilla.name,
        location:
            apiVilla.address.isNotEmpty ? apiVilla.address : apiVilla.area,
        imageUrl: apiVilla.primaryImage,
        rating: apiVilla.rating,
        price: apiVilla.effectiveWeekdayPrice,
        description:
            apiVilla.desc.isNotEmpty
                ? apiVilla.desc
                : 'Beautiful villa with excellent amenities and services.',
        isFeatured: apiVilla.isFeatured,
        latitude: apiVilla.latitude,
        longitude: apiVilla.longitude,
        villaGroupId: apiVilla.villaGroupId > 0 ? apiVilla.villaGroupId : null,
        amenities:
            amenities.isNotEmpty
                ? amenities
                : ['WiFi', 'AC', 'Parking'], // Default amenities if none found
        additionalImages:
            apiVilla.images.isNotEmpty
                ? apiVilla.images
                : [
                  apiVilla.primaryImage.isNotEmpty
                      ? apiVilla.primaryImage
                      : 'https://via.placeholder.com/400x250/cccccc/ffffff?text=No+Image',
                ],
      );
    } catch (e) {
      sl.logger.e('Error converting ApiVilla to Villa: $e');
      return null;
    }
  }

  /// Clear villa group data
  void clearVillaGroupData() {
    sl.logger.d(
      'VillaGroupProvider: Clearing villa group data (${_villaGroupVillas.length} villas)',
    );
    _villaGroupVillas = [];
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  /// Get villa by ID from current villa group list
  Villa? getVillaById(String id) {
    try {
      return _villaGroupVillas.firstWhere((villa) => villa.id == id);
    } catch (e) {
      return null;
    }
  }
}
