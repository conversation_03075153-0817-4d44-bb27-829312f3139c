import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../../core/utils/shared_preferences_helper.dart';
import '../../core/utils/service_locator.dart';
import '../../core/utils/location_service.dart';

class UserProvider extends ChangeNotifier {
  UserModel? _currentUser;
  final SharedPreferencesHelper _prefsHelper = SharedPreferencesHelper();
  String _currentLocationAddress = 'Current Location';
  bool _isLoadingLocation = false;

  // Mock user data for demonstration
  final UserModel _mockUser = UserModel(
    id: 1,
    enabled: true,
    isDeleted: false,
    name: '<PERSON>',
    dialCode: '+91',
    countryCode: 'IN',
    contactNumber: '**********',
    email: '<EMAIL>',
    gender: 'MALE',
    dob: '15-06-1990',
    profilePicture: 'https://randomuser.me/api/portraits/men/32.jpg',
    isVerified: true,
    createdTimestamp: DateTime.now().toIso8601String(),
    token: 'mock_token_123',
  );

  UserProvider() {
    // Initialize with mock user
    _currentUser = _mockUser;
    _loadUserFromPrefs();
  }

  UserModel? get currentUser => _currentUser;
  String get currentLocationAddress => _currentLocationAddress;
  bool get isLoadingLocation => _isLoadingLocation;

  // Load user from SharedPreferences (private method)
  Future<void> _loadUserFromPrefs() async {
    final userData = await _prefsHelper.getUserData();
    if (userData != null) {
      _currentUser = UserModel.fromJson(userData);
      
      // Set auth token in API service if user has a token
      if (_currentUser?.token != null) {
        sl.apiService.setAuthToken(_currentUser!.token!);
      }
      
      notifyListeners();
    }
  }

  // Public method to load user from SharedPreferences
  Future<void> loadUserFromPrefs() async {
    await _loadUserFromPrefs();
  }

  // Save user data to SharedPreferences
  Future<void> _saveUserToPrefs(UserModel user) async {
    await _prefsHelper.saveUserData(user.toJson());
    if (user.token != null) {
      await _prefsHelper.saveAuthToken(user.token!);
      // Set auth token in API service
      sl.apiService.setAuthToken(user.token!);
    }
  }

  // Set current user after login/registration
  Future<void> setCurrentUser(UserModel user) async {
    _currentUser = user;
    await _saveUserToPrefs(user);
    notifyListeners();
  }

  // Update user profile
  Future<void> updateUserProfile({
    String? name,
    String? email,
    String? profilePicture,
    String? contactNumber,
    String? gender,
    String? dob,
  }) async {
    if (_currentUser == null) return;

    _currentUser = _currentUser!.copyWith(
      name: name,
      email: email,
      profilePicture: profilePicture,
      contactNumber: contactNumber,
      gender: gender,
      dob: dob,
    );
    await _saveUserToPrefs(_currentUser!);
    notifyListeners();
  }

  // Sign out user
  Future<void> signOut() async {
    _currentUser = null;
    await _prefsHelper.clearUserData();
    await _prefsHelper.clearAuthToken();
    // Clear auth token from API service
    sl.apiService.clearAuthToken();
    notifyListeners();
  }

  // Check if user is logged in
  bool get isLoggedIn => _currentUser != null && _currentUser!.token != null;

  // Get current location address
  Future<void> loadCurrentLocationAddress() async {
    if (_isLoadingLocation) return;
    
    _isLoadingLocation = true;
    notifyListeners();
    
    try {
      String address = await LocationService.getCurrentLocationAddress();
      _currentLocationAddress = address;
    } catch (e) {
      sl.logger.e('Error loading current location address: $e');
      _currentLocationAddress = 'Location unavailable';
    } finally {
      _isLoadingLocation = false;
      notifyListeners();
    }
  }

  // Refresh location address
  Future<void> refreshLocationAddress() async {
    await loadCurrentLocationAddress();
  }
}
