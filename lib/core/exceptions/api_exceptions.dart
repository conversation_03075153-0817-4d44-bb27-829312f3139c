/// Custom exception classes for better error handling
abstract class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic data;

  const ApiException({
    required this.message,
    this.statusCode,
    this.data,
  });

  @override
  String toString() => 'ApiException: $message';
}

/// Exception for network connectivity issues
class NetworkException extends ApiException {
  const NetworkException({
    required super.message,
    super.statusCode,
    super.data,
  });

  @override
  String toString() => 'NetworkException: $message';
}

/// Exception for authentication related issues
class AuthenticationException extends ApiException {
  const AuthenticationException({
    required super.message,
    super.statusCode = 401,
    super.data,
  });

  @override
  String toString() => 'AuthenticationException: $message';
}

/// Exception for authorization related issues
class AuthorizationException extends ApiException {
  const AuthorizationException({
    required super.message,
    super.statusCode = 403,
    super.data,
  });

  @override
  String toString() => 'AuthorizationException: $message';
}

/// Exception for validation errors
class ValidationException extends ApiException {
  const ValidationException({
    required super.message,
    super.statusCode = 422,
    super.data,
  });

  @override
  String toString() => 'ValidationException: $message';
}

/// Exception for resource not found
class NotFoundException extends ApiException {
  const NotFoundException({
    required super.message,
    super.statusCode = 404,
    super.data,
  });

  @override
  String toString() => 'NotFoundException: $message';
}

/// Exception for server errors
class ServerException extends ApiException {
  const ServerException({
    required super.message,
    super.statusCode = 500,
    super.data,
  });

  @override
  String toString() => 'ServerException: $message';
}

/// Exception for timeout related issues
class TimeoutException extends ApiException {
  const TimeoutException({
    required super.message,
    super.statusCode,
    super.data,
  });

  @override
  String toString() => 'TimeoutException: $message';
}

/// Exception for bad request
class BadRequestException extends ApiException {
  const BadRequestException({
    required super.message,
    super.statusCode = 400,
    super.data,
  });

  @override
  String toString() => 'BadRequestException: $message';
}

/// Exception for conflict errors
class ConflictException extends ApiException {
  const ConflictException({
    required super.message,
    super.statusCode = 409,
    super.data,
  });

  @override
  String toString() => 'ConflictException: $message';
}

/// Exception for when request is cancelled
class RequestCancelledException extends ApiException {
  const RequestCancelledException({
    super.message = 'Request was cancelled',
    super.statusCode,
    super.data,
  });

  @override
  String toString() => 'RequestCancelledException: $message';
}