import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import '../constants/api_constants.dart';
import '../exceptions/api_exceptions.dart';

class DioClient {
  final Dio _dio;
  final Logger _logger;
  String? _authToken;

  DioClient({Dio? dio, Logger? logger})
      : _dio = dio ?? Dio(),
        _logger = logger ?? Logger() {
    _dio.options = BaseOptions(
      baseUrl: ApiConstants.baseUrl,
      connectTimeout: Duration(milliseconds: ApiConstants.connectionTimeout),
      receiveTimeout: Duration(milliseconds: ApiConstants.receiveTimeout),
      sendTimeout: Duration(milliseconds: ApiConstants.sendTimeout),
      responseType: ResponseType.json,
      headers: {
        'Content-Type': ApiConstants.contentType,
        'Accept': ApiConstants.contentType,
      },
    );
    
    // Add request interceptor for authentication
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token if available
        if (_authToken != null) {
          options.headers[ApiConstants.authorization] =
              '${ApiConstants.bearerPrefix}$_authToken';
        }
        handler.next(options);
      },
      onResponse: (response, handler) {
        _logger.d('Response: ${response.statusCode} ${response.requestOptions.path}');
        handler.next(response);
      },
      onError: (error, handler) {
        _logger.e('Error: ${error.response?.statusCode} ${error.requestOptions.path}');
        handler.next(error);
      },
    ));
    
    // Add logging interceptor
    _dio.interceptors.add(LogInterceptor(
      request: true,
      requestHeader: true,
      requestBody: true,
      responseHeader: false,
      responseBody: true,
      error: true,
      logPrint: (object) => _logger.d(object),
    ));
  }

  /// Set authentication token
  void setAuthToken(String token) {
    _authToken = token;
  }

  /// Clear authentication token
  void clearAuthToken() {
    _authToken = null;
  }

  /// GET request
  Future<dynamic> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return response.data;
    } on DioException catch (e) {
      _handleError(e);
    } catch (e) {
      _logger.e('GET Error: $e');
      throw Exception('An error occurred: $e');
    }
  }

  /// POST request
  Future<dynamic> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return response.data;
    } on DioException catch (e) {
      _handleError(e);
    } catch (e) {
      _logger.e('POST Error: $e');
      throw Exception('An error occurred: $e');
    }
  }

  /// PUT request
  Future<dynamic> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return response.data;
    } on DioException catch (e) {
      _handleError(e);
    } catch (e) {
      _logger.e('PUT Error: $e');
      throw Exception('An error occurred: $e');
    }
  }

  /// PATCH request
  Future<dynamic> patch(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return response.data;
    } on DioException catch (e) {
      _handleError(e);
    } catch (e) {
      _logger.e('PATCH Error: $e');
      throw Exception('An error occurred: $e');
    }
  }

  /// DELETE request
  Future<dynamic> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return response.data;
    } on DioException catch (e) {
      _handleError(e);
    } catch (e) {
      _logger.e('DELETE Error: $e');
      throw Exception('An error occurred: $e');
    }
  }

  /// Download file
  Future<void> download(
    String urlPath,
    String savePath, {
    ProgressCallback? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    bool deleteOnError = true,
    String lengthHeader = Headers.contentLengthHeader,
    Options? options,
  }) async {
    try {
      await _dio.download(
        urlPath,
        savePath,
        onReceiveProgress: onReceiveProgress,
        queryParameters: queryParameters,
        cancelToken: cancelToken,
        deleteOnError: deleteOnError,
        lengthHeader: lengthHeader,
        options: options,
      );
    } on DioException catch (e) {
      _handleError(e);
    } catch (e) {
      _logger.e('Download Error: $e');
      throw Exception('Download failed: $e');
    }
  }

  void _handleError(DioException e) {
    _logger.e('DioError: ${e.message}');
    
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        throw TimeoutException(
          message: 'Connection timeout. Please check your internet connection.',
          data: e.response?.data,
        );
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final responseData = e.response?.data;
        final errorMessage = _extractErrorMessage(responseData);
        
        switch (statusCode) {
          case 400:
            throw BadRequestException(
              message: errorMessage,
              statusCode: statusCode,
              data: responseData,
            );
          case 401:
            throw AuthenticationException(
              message: errorMessage.isEmpty ? 'Unauthorized. Please log in again.' : errorMessage,
              statusCode: statusCode,
              data: responseData,
            );
          case 403:
            throw AuthorizationException(
              message: errorMessage.isEmpty ? 'Forbidden. You don\'t have permission to access this resource.' : errorMessage,
              statusCode: statusCode,
              data: responseData,
            );
          case 404:
            throw NotFoundException(
              message: errorMessage.isEmpty ? 'Resource not found.' : errorMessage,
              statusCode: statusCode,
              data: responseData,
            );
          case 409:
            throw ConflictException(
              message: errorMessage,
              statusCode: statusCode,
              data: responseData,
            );
          case 422:
            throw ValidationException(
              message: errorMessage,
              statusCode: statusCode,
              data: responseData,
            );
          case 500:
          case 502:
          case 503:
            throw ServerException(
              message: errorMessage.isEmpty ? 'Server error. Please try again later.' : errorMessage,
              statusCode: statusCode,
              data: responseData,
            );
          default:
            throw ServerException(
              message: errorMessage.isEmpty ? 'Unknown server error occurred.' : errorMessage,
              statusCode: statusCode,
              data: responseData,
            );
        }
      case DioExceptionType.cancel:
        throw RequestCancelledException(
          data: e.response?.data,
        );
      case DioExceptionType.connectionError:
        throw NetworkException(
          message: 'Connection error. Please check your internet connection.',
          data: e.response?.data,
        );
      case DioExceptionType.badCertificate:
        throw NetworkException(
          message: 'Certificate verification failed.',
          data: e.response?.data,
        );
      default:
        throw NetworkException(
          message: e.message ?? 'Unknown network error occurred',
          data: e.response?.data,
        );
    }
  }

  String _extractErrorMessage(dynamic responseData) {
    if (responseData != null && responseData is Map) {
      // Try different common error message keys
      if (responseData.containsKey('message')) {
        return responseData['message'].toString();
      } else if (responseData.containsKey('error')) {
        return responseData['error'].toString();
      } else if (responseData.containsKey('detail')) {
        return responseData['detail'].toString();
      } else if (responseData.containsKey('errors')) {
        final errors = responseData['errors'];
        if (errors is List && errors.isNotEmpty) {
          return errors.first.toString();
        } else if (errors is Map) {
          return errors.values.first.toString();
        }
      }
    }
    return 'Unknown error';
  }
}
