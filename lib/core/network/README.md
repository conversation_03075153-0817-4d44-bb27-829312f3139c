# Dio Network Setup for MastVilla

This directory contains a comprehensive Dio setup for making HTTP requests to the MastVilla API (`https://api.mastvilla.com`).

## Architecture Overview

The network layer follows a clean architecture pattern with the following components:

### 1. API Constants (`api_constants.dart`)
- Contains the base URL and all API endpoints
- Defines timeout values and common headers
- Centralizes all API-related constants

### 2. Dio Client (`dio_client.dart`)
- Core HTTP client wrapper around Dio
- Handles authentication tokens automatically
- Provides interceptors for logging and error handling
- Supports all HTTP methods (GET, POST, PUT, PATCH, DELETE)
- Includes file download capabilities

### 3. API Service (`api_service.dart`)
- High-level service layer for making API calls
- Provides convenience methods for common operations
- Handles authentication token management
- Includes specific methods for villa, booking, and user operations

### 4. Service Locator (`service_locator.dart`)
- Dependency injection container
- Provides singleton instances of services
- Easy access pattern using `sl` global getter

### 5. Custom Exceptions (`api_exceptions.dart`)
- Structured error handling with specific exception types
- Better error categorization and handling
- Includes status codes and error data

### 6. API Response Wrapper (`api_response.dart`)
- Standardized response format
- Support for paginated responses
- Type-safe response handling

## Getting Started

### 1. Initialize the Service Locator

In your `main.dart`:

```dart
import 'core/utils/service_locator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize the service locator
  await sl.init();
  
  runApp(MyApp());
}
```

### 2. Making API Calls

#### Simple GET Request
```dart
try {
  final apiService = sl.apiService;
  final villas = await apiService.getVillas();
  print('Fetched ${villas.length} villas');
} on NetworkException catch (e) {
  print('Network error: ${e.message}');
} catch (e) {
  print('Unexpected error: $e');
}
```

#### POST Request with Authentication
```dart
try {
  final apiService = sl.apiService;
  
  // Login and set token
  final loginResponse = await apiService.login('<EMAIL>', 'password');
  apiService.setAuthToken(loginResponse['token']);
  
  // Create booking (authenticated request)
  final bookingData = {
    'villaId': 'villa123',
    'checkIn': '2024-07-15',
    'checkOut': '2024-07-18',
    'guests': 4,
  };
  
  final booking = await apiService.createBooking(bookingData);
  print('Booking created: ${booking['id']}');
  
} on AuthenticationException catch (e) {
  print('Login failed: ${e.message}');
} on ValidationException catch (e) {
  print('Invalid booking data: ${e.message}');
}
```

### 3. Error Handling

The setup provides specific exception types for better error handling:

```dart
try {
  await apiService.getVillaDetails('invalid-id');
} on NotFoundException catch (e) {
  // Handle 404 - villa not found
  showSnackbar('Villa not found');
} on AuthenticationException catch (e) {
  // Handle 401 - redirect to login
  navigateToLogin();
} on NetworkException catch (e) {
  // Handle network connectivity issues
  showRetryDialog();
} on TimeoutException catch (e) {
  // Handle request timeouts
  showTimeoutMessage();
} catch (e) {
  // Handle any other unexpected errors
  showGenericError();
}
```

### 4. Custom API Calls

For endpoints not covered by the convenience methods:

```dart
final apiService = sl.apiService;

// Custom GET request
final response = await apiService.get('/custom/endpoint', 
  queryParams: {'param': 'value'}
);

// Custom POST request
final response = await apiService.post('/custom/endpoint', 
  data: {'key': 'value'}
);
```

### 5. File Downloads

```dart
await apiService.downloadFile(
  '/images/villa123.jpg',
  '/local/path/villa.jpg',
  onProgress: (received, total) {
    final progress = (received / total * 100).toInt();
    print('Download progress: $progress%');
  },
);
```

## Available API Methods

### Authentication
- `login(email, password)` - User login
- `register(userData)` - User registration
- `logout()` - User logout
- `refreshToken(token)` - Refresh authentication token

### User Management
- `getUserProfile()` - Get current user profile
- `updateUserProfile(userData)` - Update user profile

### Villa Operations
- `getVillas(filters)` - Get villas with optional filters
- `getVillaDetails(villaId)` - Get specific villa details
- `searchVillas(query, filters)` - Search villas
- `getFeaturedVillas()` - Get featured villas

### Booking Operations
- `createBooking(bookingData)` - Create a new booking
- `getBookingHistory()` - Get user's booking history
- `cancelBooking(bookingId)` - Cancel a booking

### Chat Operations
- `getChats()` - Get user's chats
- `getMessages(chatId)` - Get messages for a chat
- `sendMessage(chatId, message)` - Send a message

## Configuration

### Timeouts
Default timeouts are set in `ApiConstants`:
- Connection timeout: 30 seconds
- Receive timeout: 30 seconds
- Send timeout: 30 seconds

### Headers
Default headers include:
- `Content-Type: application/json`
- `Accept: application/json`
- `Authorization: Bearer {token}` (when authenticated)

### Logging
The setup includes comprehensive logging that can be configured in the `ServiceLocator`. Logs include:
- Request details (URL, headers, body)
- Response details (status, headers, body)
- Error information

## Best Practices

1. **Always handle specific exceptions** rather than generic `catch (e)`
2. **Use the service locator** (`sl`) for accessing API services
3. **Set authentication tokens** after successful login
4. **Clear tokens** on logout or authentication errors
5. **Use cancellation tokens** for long-running requests that users might cancel
6. **Handle network connectivity** gracefully with appropriate user feedback

## Testing

For testing, you can reset the service locator:

```dart
void main() {
  setUp(() {
    sl.reset(); // Reset all instances
  });
  
  // Your tests here
}
```

## Examples

See `usage_example.dart` for comprehensive examples of how to use the network layer.