import 'package:dio/dio.dart';
import '../utils/service_locator.dart';
import '../exceptions/api_exceptions.dart';
import 'api_response.dart';

/// Example demonstrating how to use the Dio setup for network calls
class NetworkUsageExample {
  
  /// Example: Login user
  static Future<void> loginExample() async {
    try {
      // Using the service locator to get ApiService
      final apiService = sl.apiService;
      
      // Make login request
      final response = await apiService.login(
        '<EMAIL>', 
        'password123'
      );
      
      // Handle successful response
      print('Login successful: $response');
      
      // Extract token from response (assuming response structure)
      if (response['token'] != null) {
        // Set the auth token for future requests
        apiService.setAuthToken(response['token']);
      }
      
    } on AuthenticationException catch (e) {
      print('Authentication failed: ${e.message}');
    } on NetworkException catch (e) {
      print('Network error: ${e.message}');
    } on TimeoutException catch (e) {
      print('Request timeout: ${e.message}');
    } catch (e) {
      print('Unexpected error: $e');
    }
  }
  
  /// Example: Get villas with filters
  static Future<void> getVillasExample() async {
    try {
      final apiService = sl.apiService;
      
      // Define search filters
      final filters = {
        'location': 'Goa',
        'minPrice': 5000,
        'maxPrice': 15000,
        'guests': 4,
        'page': 1,
        'limit': 10,
      };
      
      // Make API call
      final response = await apiService.getVillas(filters: filters);
      
      print('Villas fetched successfully: ${response.length} villas found');
      
    } on NotFoundException catch (e) {
      print('No villas found: ${e.message}');
    } on ApiException catch (e) {
      print('API error: ${e.message} (Status: ${e.statusCode})');
    } catch (e) {
      print('Unexpected error: $e');
    }
  }
  
  /// Example: Create a booking
  static Future<void> createBookingExample() async {
    try {
      final apiService = sl.apiService;
      
      // Booking data
      final bookingData = {
        'villaId': 'villa123',
        'checkIn': '2024-07-15',
        'checkOut': '2024-07-18',
        'guests': 4,
        'totalAmount': 12000,
        'paymentMethod': 'card',
      };
      
      // Create booking
      final response = await apiService.createBooking(bookingData);
      
      print('Booking created successfully: ${response['bookingId']}');
      
    } on ValidationException catch (e) {
      print('Validation error: ${e.message}');
      // Handle validation errors (e.g., show form errors)
    } on ConflictException catch (e) {
      print('Booking conflict: ${e.message}');
      // Handle booking conflicts (e.g., villa not available)
    } on AuthenticationException catch (e) {
      print('Please log in to make a booking: ${e.message}');
      // Redirect to login
    } catch (e) {
      print('Booking failed: $e');
    }
  }
  
  /// Example: Using ApiResponse wrapper
  static Future<ApiResponse<Map<String, dynamic>>> getVillaDetailsWithWrapper(
    String villaId
  ) async {
    try {
      final apiService = sl.apiService;
      
      final response = await apiService.getVillaDetails(villaId);
      
      return ApiResponse.success(
        data: response,
        message: 'Villa details fetched successfully',
      );
      
    } on ApiException catch (e) {
      return ApiResponse.error(
        message: e.message,
        statusCode: e.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'An unexpected error occurred',
      );
    }
  }
  
  /// Example: Download file with progress
  static Future<void> downloadImageExample() async {
    try {
      final apiService = sl.apiService;
      
      await apiService.downloadFile(
        '/images/villa123/main.jpg',
        '/storage/downloads/villa_image.jpg',
        onProgress: (received, total) {
          if (total != -1) {
            final progress = (received / total * 100).toStringAsFixed(1);
            print('Download progress: $progress%');
          }
        },
      );
      
      print('Image downloaded successfully');
      
    } on ApiException catch (e) {
      print('Download failed: ${e.message}');
    }
  }
  
  /// Example: Handle request cancellation
  static Future<void> searchWithCancellationExample() async {
    // Create a cancel token
    final cancelToken = CancelToken();
    
    // Cancel the request after 5 seconds (example)
    Future.delayed(Duration(seconds: 5), () {
      cancelToken.cancel('Search cancelled by user');
    });
    
    try {
      final apiService = sl.apiService;
      
      final response = await apiService.searchVillas(
        'beachfront villa',
        filters: {'location': 'Goa'},
      );
      
      print('Search completed: ${response.length} results');
      
    } on RequestCancelledException catch (e) {
      print('Search was cancelled: ${e.message}');
    } catch (e) {
      print('Search error: $e');
    }
  }
  
  /// Example: Logout and clear token
  static Future<void> logoutExample() async {
    try {
      final apiService = sl.apiService;
      
      // Call logout endpoint (this will also clear the token)
      await apiService.logout();
      
      print('Logged out successfully');
      
    } catch (e) {
      // Even if logout API fails, clear the local token
      sl.apiService.clearAuthToken();
      print('Logged out locally due to error: $e');
    }
  }
}

/// Helper class for making custom API calls
class CustomApiHelper {
  static final _apiService = sl.apiService;
  
  /// Example of a custom endpoint call
  static Future<dynamic> getCustomData(String endpoint) async {
    try {
      return await _apiService.get(endpoint);
    } catch (e) {
      rethrow;
    }
  }
  
  /// Example of posting custom data
  static Future<dynamic> postCustomData(
    String endpoint, 
    Map<String, dynamic> data
  ) async {
    try {
      return await _apiService.post(endpoint, data: data);
    } catch (e) {
      rethrow;
    }
  }
}