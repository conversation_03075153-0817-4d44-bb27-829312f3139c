import 'package:intl/intl.dart';

class DateUtils {
  static const String apiDateFormat = 'yyyy-MM-dd';
  
  /// Get current date in API format (yyyy-MM-dd)
  static String getCurrentDate() {
    final now = DateTime.now();
    return DateFormat(apiDateFormat).format(now);
  }
  
  /// Get date after specified number of days from today in API format
  static String getDateAfterDays(int days) {
    final now = DateTime.now();
    final futureDate = now.add(Duration(days: days));
    return DateFormat(apiDateFormat).format(futureDate);
  }
  
  /// Get date range for dashboard API (from today to next 7 days)
  static Map<String, String> getDashboardDateRange() {
    return {
      'fromDate': getCurrentDate(),
      'toDate': getDateAfterDays(7),
    };
  }
  
  /// Format date for display purposes
  static String formatDateForDisplay(DateTime date) {
    return DateFormat('MMM dd, yyyy').format(date);
  }
  
  /// Format date for display with day name
  static String formatDateWithDay(DateTime date) {
    return DateFormat('EEE, MMM dd').format(date);
  }
  
  /// Parse API date string to DateTime
  static DateTime? parseApiDate(String dateString) {
    try {
      return DateFormat(apiDateFormat).parse(dateString);
    } catch (e) {
      return null;
    }
  }
  
  /// Check if a date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }
  
  /// Check if a date is tomorrow
  static bool isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return date.year == tomorrow.year && 
           date.month == tomorrow.month && 
           date.day == tomorrow.day;
  }
}