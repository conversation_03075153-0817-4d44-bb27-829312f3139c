import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../../data/datasources/api_service.dart';
import '../network/dio_client.dart';

class ServiceLocator {
  // Singleton pattern
  ServiceLocator._();
  static final ServiceLocator _instance = ServiceLocator._();
  static ServiceLocator get instance => _instance;

  // Private instances to ensure singleton behavior
  Logger? _logger;
  Dio? _dio;
  DioClient? _dioClient;
  ApiService? _apiService;

  /// Get singleton Logger instance
  Logger get logger {
    _logger ??= Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      ),
    );
    return _logger!;
  }

  /// Get singleton Dio instance
  Dio get dio {
    _dio ??= Dio();
    return _dio!;
  }

  /// Get singleton DioClient instance
  DioClient get dioClient {
    _dioClient ??= DioClient(dio: dio, logger: logger);
    return _dioClient!;
  }

  /// Get singleton ApiService instance
  ApiService get apiService {
    _apiService ??= ApiService(dioClient: dioClient);
    return _apiService!;
  }

  /// Reset all instances (useful for testing)
  void reset() {
    _logger = null;
    _dio = null;
    _dioClient = null;
    _apiService = null;
  }

  /// Initialize all services (call this in main.dart)
  Future<void> init() async {
    // Initialize logger
    logger;
    
    // Initialize network layer
    dioClient;
    
    // Initialize API service
    apiService;
    
    logger.i('ServiceLocator initialized successfully');
  }
}

/// Global getter for easy access
ServiceLocator get sl => ServiceLocator.instance;
