import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geocoding/geocoding.dart';
import 'service_locator.dart';

class LocationService {
  static const double _defaultLatitude = 19.281112;  // Default to Mumbai
  static const double _defaultLongitude = 73.047047;

  /// Get current location with proper permission handling
  static Future<Position> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        sl.logger.w('Location services are disabled');
        return _getDefaultPosition();
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          sl.logger.w('Location permission denied');
          return _getDefaultPosition();
        }
      }

      if (permission == LocationPermission.deniedForever) {
        sl.logger.w('Location permission denied forever');
        return _getDefaultPosition();
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium,
        timeLimit: const Duration(seconds: 10),
      );

      sl.logger.d('Current location: ${position.latitude}, ${position.longitude}');
      return position;
    } catch (e) {
      sl.logger.e('Error getting current location: $e');
      return _getDefaultPosition();
    }
  }

  /// Get cached location or current location
  static Future<Position> getCachedOrCurrentLocation() async {
    try {
      // Try to get last known position first (faster)
      Position? lastPosition = await Geolocator.getLastKnownPosition();
      if (lastPosition != null) {
        // Check if the last position is recent enough (within 1 hour)
        final now = DateTime.now();
        final positionTime = lastPosition.timestamp;
        final timeDifference = now.difference(positionTime);
        
        if (timeDifference.inHours < 1) {
          sl.logger.d('Using cached location: ${lastPosition.latitude}, ${lastPosition.longitude}');
          return lastPosition;
        }
      }

      // If no recent cached position, get current location
      return await getCurrentLocation();
    } catch (e) {
      sl.logger.e('Error getting cached or current location: $e');
      return _getDefaultPosition();
    }
  }

  /// Check if location permission is granted
  static Future<bool> hasLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      return permission == LocationPermission.always ||
             permission == LocationPermission.whileInUse;
    } catch (e) {
      sl.logger.e('Error checking location permission: $e');
      return false;
    }
  }

  /// Request location permission
  static Future<bool> requestLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.requestPermission();
      return permission == LocationPermission.always ||
             permission == LocationPermission.whileInUse;
    } catch (e) {
      sl.logger.e('Error requesting location permission: $e');
      return false;
    }
  }

  /// Open app settings for location permission
  static Future<void> openLocationSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      sl.logger.e('Error opening location settings: $e');
    }
  }

  /// Get default position (fallback)
  static Position _getDefaultPosition() {
    return Position(
      latitude: _defaultLatitude,
      longitude: _defaultLongitude,
      timestamp: DateTime.now(),
      accuracy: 0,
      altitude: 0,
      altitudeAccuracy: 0,
      heading: 0,
      headingAccuracy: 0,
      speed: 0,
      speedAccuracy: 0,
    );
  }

  /// Calculate distance between two points in kilometers
  static double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    ) / 1000; // Convert meters to kilometers
  }

  /// Convert coordinates to string format for API calls
  static String latitudeToString(double latitude) {
    return latitude.toStringAsFixed(6);
  }

  static String longitudeToString(double longitude) {
    return longitude.toStringAsFixed(6);
  }

  /// Get address from coordinates using reverse geocoding
  static Future<String> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        
        // Build address string from available components
        List<String> addressComponents = [];
        
        if (place.street != null && place.street!.isNotEmpty) {
          addressComponents.add(place.street!);
        }
        if (place.subLocality != null && place.subLocality!.isNotEmpty) {
          addressComponents.add(place.subLocality!);
        }
        if (place.locality != null && place.locality!.isNotEmpty) {
          addressComponents.add(place.locality!);
        }
        if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
          addressComponents.add(place.administrativeArea!);
        }
        
        String address = addressComponents.join(', ');
        
        // Fallback to basic location info if no detailed address
        if (address.isEmpty) {
          if (place.locality != null && place.locality!.isNotEmpty) {
            address = place.locality!;
            if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
              address += ', ${place.administrativeArea!}';
            }
          } else if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
            address = place.administrativeArea!;
          } else if (place.country != null && place.country!.isNotEmpty) {
            address = place.country!;
          } else {
            address = 'Unknown Location';
          }
        }
        
        sl.logger.d('Address from coordinates: $address');
        return address;
      } else {
        sl.logger.w('No placemarks found for coordinates');
        return 'Location unavailable';
      }
    } catch (e) {
      sl.logger.e('Error getting address from coordinates: $e');
      return 'Location unavailable';
    }
  }

  /// Get current location address
  static Future<String> getCurrentLocationAddress() async {
    try {
      Position position = await getCachedOrCurrentLocation();
      return await getAddressFromCoordinates(position.latitude, position.longitude);
    } catch (e) {
      sl.logger.e('Error getting current location address: $e');
      return 'Location unavailable';
    }
  }
}